﻿using System.Globalization;

namespace GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate
{
    public class ReleaseHubrfqSendMailTemplateQuery : IRequest<BaseResponse<string>>
    {
        public required string HUBRFQStatus { get; set; }
        public required string Code { get; set; }
        public required string ClientCurrencyCode { get; set; }
        public required int BomId { get; set; }
        public required int LoginId { get; set; }
        public required int ClientId { get; set; }
        public required bool IsReleasedAll { get; set; }
        public required bool IsPoHub { get; set; }  
        public int? RequirementId { get; set; }
        public required CultureInfo CultureInfo { get; set; }
        public int ClientCurrencyId { get; set; }
    }
}

﻿namespace GlobalTrader2.Aggregator.UseCases.Orders.BOM.CustomerRequirementNoBidFlow.Commands
{
    public record UpdateCustomerRequirementNoBidFlowCommand : IRequest<BaseResponse<int>>
    {
        public int CustomerRequirementId { get; set; }
        public int? UpdatedBy { get; set; }
        public int BomId { get; set; }
        public string? Notes { get; set; }
        public string? BomCode { get; set; }
        public string? BomName { get; set; }
        public string? BomCompanyName { get; set; }
        public int? BomCompanyNo { get; set; }
        public string? SalesManName { get; set; }
        public int SalesManNo { get; set; }
        public required string Subject { get; set; }
        public required string LoginEmail { get; set; }
        public required string Sender { get; set; }
        public required int LoginId { get; set; }
        public required string StatusMessage { get; set; }
        public int ClientId { get; set; }
        public required string ClientName { get; set; }
        public required string ClientCurrencyCode { get; set; }
        public bool IsPoHub { get; set; }
    }
}
﻿using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries.Dtos;

namespace GlobalTrader2.Dto.Templates
{
    public class ReleasedHUBRFQMailTemplate
    {
        public required string HUBRFQStatus { get; set; }
        public required string Code { get; set; }
        public required string Contact { get; set; }
        public required string Name { get; set; }
        public required string Currency { get; set; }
        public required string Company { get; set; }
        public required DateTime QuoteRequired { get; set; }
        public required string ClientCurrencyCode { get; set; }
        public int? RequirementNumber { get; set; }
        public bool IsAllNoBid { get; set; } = false;
        public bool IsAllReleased { get; set; } = false;
        public bool IsPoHub { get; set; }
        public List<BOMListForCustomerRequirementDto> Requirements { get; set; } = [];
        public List<EnhancedListForBomSourcingResultDto> SourcingResults { get; set; } = [];
    }
}

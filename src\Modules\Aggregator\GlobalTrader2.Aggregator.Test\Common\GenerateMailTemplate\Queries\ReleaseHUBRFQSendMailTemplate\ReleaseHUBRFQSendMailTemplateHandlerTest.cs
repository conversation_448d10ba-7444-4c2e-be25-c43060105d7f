using AutoFixture;
using AutoMapper;
using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.ConvertCurrency;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.CurrencyRateAtDate;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.Orders.Quotes;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.Sourcing;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.BOM.GetListForBOMSourcingResult.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingLog;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingResultQuotes;
using MediatR;
using Moq;
using System.Globalization;

namespace GlobalTrader2.Aggregator.Test.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate
{
    public class ReleaseHUBRFQSendMailTemplateHandlerTest
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<IRazorViewToStringService> _razorViewToStringServiceMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly ReleaseHubrfqSendMailTemplateHandler _handler;
        private readonly IFixture _fixture;

        public ReleaseHUBRFQSendMailTemplateHandlerTest()
        {
            _fixture = new Fixture();
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mediatorMock = new Mock<IMediator>();
            _razorViewToStringServiceMock = new Mock<IRazorViewToStringService>();
            _mapperMock = new Mock<IMapper>();

            _handler = new ReleaseHubrfqSendMailTemplateHandler(
                _mediatorMock.Object,
                _razorViewToStringServiceMock.Object,
                _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_WhenValidRequestWithReleasedAll_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest(isReleasedAll: true);
            SetupBasicMockResponses();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task Handle_WhenValidRequestWithoutReleasedAll_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest(isReleasedAll: false);
            SetupBasicMockResponses();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task Handle_WhenIsReleasedAllTrue_ShouldCallGetListForBomSourcingResultQuery()
        {
            // Arrange
            var request = CreateValidRequest(isReleasedAll: true);
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetListForBomSourcingResultQuery>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WhenIsReleasedAllFalse_ShouldNotCallGetListForBomSourcingResultQuery()
        {
            // Arrange
            var request = CreateValidRequest(isReleasedAll: false);
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetListForBomSourcingResultQuery>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldCallRazorViewService()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _razorViewToStringServiceMock.Verify(r => r.RenderViewToStringAsync(
                It.IsAny<string>(),
                It.IsAny<object>()), Times.Once);
        }

        private ReleaseHubrfqSendMailTemplateQuery CreateValidRequest(bool isReleasedAll = true)
        {
            return new ReleaseHubrfqSendMailTemplateQuery
            {
                BomId = 123,
                LoginId = 456,
                ClientId = 789,
                HUBRFQStatus = "Active",
                Code = "BOM001",
                ClientCurrencyCode = "USD",
                IsReleasedAll = isReleasedAll,
                IsPoHub = false,
                RequirementId = 101,
                CultureInfo = new CultureInfo("en-US"),
                ClientCurrencyId = 1
            };
        }

        private void SetupBasicMockResponses()
        {
            // Setup GetBOMDetailsQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetBOMDetailsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<BOMDetailsDto>
                {
                    Success = true,
                    Data = _fixture.Create<BOMDetailsDto>()
                });

            // Setup GetBOMListForCustomerRequirementQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetBOMListForCustomerRequirementQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IList<BOMListForCustomerRequirementDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<BOMListForCustomerRequirementDto>(2).ToList()
                });

            // Setup GetListForBomSourcingResultQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetListForBomSourcingResultQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IList<ListForBomSourcingResultDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<ListForBomSourcingResultDto>(2).ToList()
                });

            // Setup GetSourcingResultQuotesQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetSourcingResultQuotesQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IEnumerable<QuoteDetailsDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<QuoteDetailsDto>(2)
                });

            // Setup GetSourcingLogQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetSourcingLogQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IEnumerable<SourcingLogDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<SourcingLogDto>(1)
                });

            // Setup GetCurrentAtDateQuery response (for currency conversion)
            var currentAtDateDto = new CurrentAtDateDto { ExchangeRate = 1.0 };
            var currentAtDateResponse = new BaseResponse<CurrentAtDateDto>
            {
                Success = true,
                Data = currentAtDateDto
            };
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetCurrentAtDateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(currentAtDateResponse);

            // Setup GetConvertedValueQuery response (for currency conversion)
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetConvertedValueQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1.0);

            // Setup mapper
            _mapperMock.Setup(m => m.Map<EnhancedListForBomSourcingResultDto>(It.IsAny<ListForBomSourcingResultDto>()))
                .Returns(_fixture.Create<EnhancedListForBomSourcingResultDto>());

            // Setup razor view service
            _razorViewToStringServiceMock.Setup(r => r.RenderViewToStringAsync(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync("Rendered email content");
        }
    }
}

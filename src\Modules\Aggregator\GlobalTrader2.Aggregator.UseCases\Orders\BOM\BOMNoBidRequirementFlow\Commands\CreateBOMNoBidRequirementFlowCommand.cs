﻿namespace GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMNoBidRequirementFlow.Commands
{
    public record CreateBOMNoBidRequirementFlowCommand : IRequest<BaseResponse<int>>
    {
        public int BomId { get; set; }
        public string? BOMCode { get; set; }
        public string? BOMName { get; set; }
        public string? BomCompanyName { get; set; }
        public int BomCompanyNo { get; set; }
        public int UpdatedBy { get; set; }
        public string? Notes { get; set; }
        public int[] SalesmanNo { get; set; } = [];
        public required string Subject { get; set; }
        public required string LoginEmail { get; set; }
        public int LoginId { get; set; }
        public int ClientId { get; set; }
        public required string SenderName { get; set; } = string.Empty;
        public required string HUBRFQStatus { get; set; } = string.Empty;
        public required string ClientName { get; set; } = string.Empty;
        public required bool IsPoHub { get; set; }
        public required string ClientCurrencyCode { get; set; }
    }
}
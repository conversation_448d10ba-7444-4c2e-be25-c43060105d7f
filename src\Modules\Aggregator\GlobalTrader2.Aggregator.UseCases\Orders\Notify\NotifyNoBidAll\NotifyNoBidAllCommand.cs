namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyNoBidAll
{
    public class NotifyNoBidAllCommand : IRequest<BaseResponse<int>>
    {
        public int BOMId { get; set; }
        public int[] ToLogins { get; set; } = [];
        public string BOMName { get; set; } = string.Empty;
        public int SenderLoginNo { get; set; }
        public string SenderName { get; set; } = string.Empty;
        public string? SenderEmail { get; set; }
        public string Subject { get; set; } = string.Empty;
        // For mail template
        public required string HUBRFQStatus { get; set; } = string.Empty;
        public required string Code { get; set; } = string.Empty;
        public int ClientId { get; set; }
        public bool IsPoHub { get; set; }
        public required string ClientCurrencyCode { get; set; }
        public required int LoginId { get; set; }
    }
}

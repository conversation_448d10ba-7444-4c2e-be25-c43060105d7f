using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseAll;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Settings.UseCases.SecuritySettings.SecurityUsers.Queries;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom
{
    public class NotifyReleaseBomHandler(IMediator mediator, IEmailService emailService, IRazorViewToStringService razorViewToStringService) : IRequestHandler<NotifyReleaseBomCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IEmailService _emailService = emailService;
        private readonly IRazorViewToStringService _razorViewToStringService = razorViewToStringService;

        public async Task<BaseResponse<int>> Handle(NotifyReleaseBomCommand request, CancellationToken cancellationToken)
        {
            var getUserQuery = new GetSecurityUserProfileQuery(request.SenderLoginNo);
            var getUserResponse = await _mediator.Send(getUserQuery, cancellationToken);

            // Process individual logins
            var recipients = request.ToLogins
                .Select(x => new RecipientRequest(
                    Value: x,
                    Type: (int)MailMessageAddressType.Individual
                )).ToList();


            // Process mail groups
            foreach (var groupId in request.ToGroups)
            {
                recipients.Add(new RecipientRequest(
                    Value: groupId,
                    Type: (int)MailMessageAddressType.Group
                ));
            }

            var contentInternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/_NotifyReleaseBom", new Dto.Templates.NotifyReleaseBom()
            {
                HyperLink = request.HUBRFQHyperlink,
                BomName = request.BOMName,
            });

            var sendMessageCommand = new SendNewMessageCommand(request.Subject, contentInternal, recipients, request.SenderLoginNo, request.SenderName)
            {
                CompanyId = request.BomCompanyNo,
                Message = contentInternal,
                Recipients = recipients,
                SenderLoginNo = request.SenderLoginNo,
                SenderName = request.SenderName,
                Subject = request.Subject
            };
            var result = await _mediator.Send(sendMessageCommand, cancellationToken);

            var contentExternal = (await _mediator.Send(new ReleaseHubrfqSendMailTemplateQuery()
            {
                HUBRFQStatus = request.HUBRFQStatus,
                Code = request.Code,
                ClientCurrencyCode = request.ClientCurrencyCode,
                BomId = request.BOMId,
                LoginId = request.LoginId,
                ClientId = request.ClientId,
                IsReleasedAll = true,
                IsPoHub = request.IsPoHub,
                RequirementId = request.RequirementID,
                CultureInfo = request.CultureInfo,
                ClientCurrencyId = request.ClientCurrencyID,
            }, cancellationToken)).Data;

            var loginPreferenceResponse = await _mediator.Send(new LoginPreferenceDetailsCommand { LoginNo = request.SenderLoginNo }, cancellationToken);
            var toEmail = getUserResponse.Data?.EMail;
            var sendEmail = loginPreferenceResponse.Data?.SendEmail;
            if (sendEmail is true && !string.IsNullOrWhiteSpace(toEmail))
            {
                await _emailService.TrySendEmailAsync(request.SenderEmail!, [toEmail], [], [], request.Subject, contentExternal ?? "", [], [], cancellationToken);
            }
            return result;
        }
    }
}

using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseAll;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.LoginManager;
using GlobalTrader2.Dto.SecuritySettings;
using GlobalTrader2.Settings.UseCases.SecuritySettings.SecurityUsers.Queries;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;
using MediatR;
using Moq;
using System.Globalization;

namespace GlobalTrader2.Aggregator.Test.Orders.Notify.NotifyReleaseAll
{
    public class NotifyReleaseAllHandlerTest
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<IEmailService> _emailServiceMock;
        private readonly Mock<IRazorViewToStringService> _razorViewToStringServiceMock;
        private readonly NotifyReleaseBomHandler _handler;
        private readonly IFixture _fixture;

        public NotifyReleaseAllHandlerTest()
        {
            _fixture = new Fixture();
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mediatorMock = new Mock<IMediator>();
            _emailServiceMock = new Mock<IEmailService>();
            _razorViewToStringServiceMock = new Mock<IRazorViewToStringService>();

            _handler = new NotifyReleaseBomHandler(
                _mediatorMock.Object,
                _emailServiceMock.Object,
                _razorViewToStringServiceMock.Object);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);

            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetSecurityUserProfileQuery>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WhenRequestIsValid_ShouldCallMediatorMultipleTimes()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _mediatorMock.Verify(m => m.Send(
                It.IsAny<SendNewMessageCommand>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WhenRequestIsValid_ShouldCallRazorViewService()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _razorViewToStringServiceMock.Verify(r => r.RenderViewToStringAsync(
                It.IsAny<string>(),
                It.IsAny<object>()), Times.AtLeastOnce);
        }

        private NotifyReleaseBomCommand CreateValidRequest()
        {
            return new NotifyReleaseBomCommand
            {
                BOMId = 123,
                ToLogins = [456, 789],
                ToGroups = [],
                BOMName = "Test BOM",
                SenderLoginNo = 101,
                SenderName = "Test Sender",
                SenderEmail = "<EMAIL>",
                Subject = "Test Subject",
                IsReleasedAll = true,
                HUBRFQStatus = "Active",
                Code = "BOM001",
                ClientId = 102,
                ClientCurrencyCode = "USD",
                CultureInfo = new CultureInfo("en-US"),
                ClientCurrencyID = 1,
                IsPoHub = true,
                LoginId = 103
            };
        }

        private void SetupBasicMockResponses()
        {
            // Setup GetSecurityUserProfileQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetSecurityUserProfileQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<SecurityUserProfileDto>
                {
                    Success = true,
                    Data = new SecurityUserProfileDto { EMail = "<EMAIL>" }
                });

            // Setup LoginPreferenceDetailsCommand response
            _mediatorMock.Setup(m => m.Send(It.IsAny<LoginPreferenceDetailsCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<LoginPreferenceDetailsDto>
                {
                    Success = true,
                    Data = new LoginPreferenceDetailsDto { SendEmail = true }
                });

            // Setup SendNewMessageCommand response
            _mediatorMock.Setup(m => m.Send(It.IsAny<SendNewMessageCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<int> { Success = true, Data = 1 });

            // Setup ReleaseHubrfqSendMailTemplateQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<ReleaseHubrfqSendMailTemplateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = "Test email content" });

            // Setup razor view service
            _razorViewToStringServiceMock.Setup(r => r.RenderViewToStringAsync(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync("Rendered content");

            // Setup email service
            _emailServiceMock.Setup(e => e.TrySendEmailAsync(
                It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<List<string>>(),
                It.IsAny<List<string>>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<List<string>>(), It.IsAny<List<System.Net.Mail.Attachment>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
        }
    }
}
using AutoFixture;
using AutoMapper;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseAll;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.ConvertCurrency;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.CurrencyRateAtDate;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.LoginManager;
using GlobalTrader2.Dto.Orders.Quotes;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.SecuritySettings;
using GlobalTrader2.Dto.Sourcing;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.BOM.GetListForBOMSourcingResult.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingLog;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingResultQuotes;
using GlobalTrader2.Settings.UseCases.SecuritySettings.SecurityUsers.Queries;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;
using MediatR;
using Moq;
using System.Globalization;

namespace GlobalTrader2.Aggregator.Test.Orders.Notify.NotifyReleaseAll
{
    public class NotifyReleaseAllHandlerTest
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<IEmailService> _emailServiceMock;
        private readonly Mock<IRazorViewToStringService> _razorViewToStringServiceMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly NotifyReleaseBomHandler _handler;
        private readonly IFixture _fixture;

        public NotifyReleaseAllHandlerTest()
        {
            _fixture = new Fixture();
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mediatorMock = new Mock<IMediator>();
            _emailServiceMock = new Mock<IEmailService>();
            _razorViewToStringServiceMock = new Mock<IRazorViewToStringService>();
            _mapperMock = new Mock<IMapper>();

            _handler = new NotifyReleaseBomHandler(
                _mediatorMock.Object,
                _emailServiceMock.Object,
                _razorViewToStringServiceMock.Object,
                _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);

            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetCurrentAtDateQuery>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        [Fact]
        public async Task Handle_WhenRequestIsValid_ShouldCallMediatorMultipleTimes()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetCurrentAtDateQuery>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        [Fact]
        public async Task Handle_WhenRequestIsValid_ShouldCallRazorViewService()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _razorViewToStringServiceMock.Verify(r => r.RenderViewToStringAsync(
                It.IsAny<string>(),
                It.IsAny<object>()), Times.AtLeastOnce);
        }

        private NotifyReleaseBomCommand CreateValidRequest()
        {
            return new NotifyReleaseBomCommand
            {
                BOMId = 123,
                ToLogins = [456, 789],
                ToGroups = [],
                BOMName = "Test BOM",
                SenderLoginNo = 101,
                SenderName = "Test Sender",
                SenderEmail = "<EMAIL>",
                Subject = "Test Subject",
                IsReleasedAll = true,
                HUBRFQStatus = "Active",
                Code = "BOM001",
                ClientId = 102,
                ClientCurrencyCode = "USD",
                CultureInfo = new CultureInfo("en-US"),
                ClientCurrencyID = 1,
                IsPoHub = true,
                LoginId = 103
            };
        }

        private void SetupBasicMockResponses()
        {
            // Setup GetSecurityUserProfileQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetSecurityUserProfileQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<SecurityUserProfileDto>
                {
                    Success = true,
                    Data = new SecurityUserProfileDto { EMail = "<EMAIL>" }
                });

            // Setup GetListForBomSourcingResultQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetListForBomSourcingResultQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IList<ListForBomSourcingResultDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<ListForBomSourcingResultDto>(2).ToList()
                });

            // Setup GetBOMListForCustomerRequirementQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetBOMListForCustomerRequirementQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IList<BOMListForCustomerRequirementDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<BOMListForCustomerRequirementDto>(2).ToList()
                });

            // Setup GetBOMDetailsQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetBOMDetailsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<BOMDetailsDto>
                {
                    Success = true,
                    Data = _fixture.Create<BOMDetailsDto>()
                });

            // Setup LoginPreferenceDetailsCommand response
            _mediatorMock.Setup(m => m.Send(It.IsAny<LoginPreferenceDetailsCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<LoginPreferenceDetailsDto>
                {
                    Success = true,
                    Data = new LoginPreferenceDetailsDto { SendEmail = true }
                });

            // Setup SendNewMessageCommand response
            _mediatorMock.Setup(m => m.Send(It.IsAny<SendNewMessageCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<int> { Success = true, Data = 1 });

            // Setup GetSourcingResultQuotesQuery response
            var quoteDetailsList = new List<QuoteDetailsDto>
            {
                new QuoteDetailsDto { QuoteId = 1, QuoteNumber = 12345 },
                new QuoteDetailsDto { QuoteId = 2, QuoteNumber = 12346 }
            };
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetSourcingResultQuotesQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IEnumerable<QuoteDetailsDto>>
                {
                    Success = true,
                    Data = quoteDetailsList
                });

            // Setup GetSourcingLogQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetSourcingLogQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IEnumerable<SourcingLogDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<SourcingLogDto>(1)
                });

            // Setup GetCurrentAtDateQuery response (for currency conversion)
            var currentAtDateDto = new CurrentAtDateDto { ExchangeRate = 1.0 };
            var currentAtDateResponse = new BaseResponse<CurrentAtDateDto>
            {
                Success = true,
                Data = currentAtDateDto
            };
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetCurrentAtDateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(currentAtDateResponse);

            // Setup GetConvertedValueQuery response (for currency conversion)
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetConvertedValueQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1.0);

            // Setup razor view service
            _razorViewToStringServiceMock.Setup(r => r.RenderViewToStringAsync(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync("Rendered content");

            // Setup email service
            _emailServiceMock.Setup(e => e.TrySendEmailAsync(
                It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<List<string>>(),
                It.IsAny<List<string>>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<List<string>>(), It.IsAny<List<System.Net.Mail.Attachment>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            // Setup mapper
            _mapperMock.Setup(m => m.Map<List<ListForBomSourcingResultDto>>(It.IsAny<object>()))
                .Returns(_fixture.CreateMany<ListForBomSourcingResultDto>(2).ToList());

            // Setup mapper for EnhancedListForBomSourcingResultDto mapping
            _mapperMock.Setup(m => m.Map<EnhancedListForBomSourcingResultDto>(It.IsAny<ListForBomSourcingResultDto>()))
                .Returns((ListForBomSourcingResultDto source) => new EnhancedListForBomSourcingResultDto
                {
                    SourcingResultId = source.SourcingResultId,
                    CustomerRequirementNo = source.CustomerRequirementNo,
                    SourcingTable = source.SourcingTable,
                    Part = source.Part,
                    ManufacturerName = source.ManufacturerName,
                    Price = source.Price,
                    CurrencyCode = source.CurrencyCode,
                    SupplierName = source.SupplierName,
                    QuoteNumbers = new List<string>(), // Will be set by the handler
                    HighlightPartOrNotes = false,
                    HighlightManufacturer = false,
                    HighlightProductOrPackage = false,
                    HighlightQuantityOrDeliveryDate = false,
                    HighlightUpliftPrice = false,
                    HighlightRegion = false,
                    HighlightEstimatedShippingCost = false
                });
        }
    }
}
using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Settings.UseCases.SecuritySettings.SecurityUsers.Queries;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyNoBidAll
{
    public class NotifyNoBidAllHandler(IMediator mediator, IEmailService emailService, IRazorViewToStringService razorViewToStringService) : IRequestHandler<NotifyNoBidAllCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IEmailService _emailService = emailService;
        private readonly IRazorViewToStringService _razorViewToStringService = razorViewToStringService;

        public async Task<BaseResponse<int>> Handle(NotifyNoBidAllCommand request, CancellationToken cancellationToken)
        {
            var getUserQuery = new GetSecurityUserProfileQuery(request.SenderLoginNo);
            var getUserResponse = await _mediator.Send(getUserQuery, cancellationToken);

            var recipient = request.ToLogins
                .Select(x => new RecipientRequest(
                    Value: x,
                    Type: (int)MailMessageAddressType.Individual
                )).ToList();

            var contentInternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/_NotifyNoBidHUBRFQEMail", new NotifyNoBidHUBRFQTemplate()
            {
                HyperLink = $"/Orders/HUBRFQ/Details?bom={request.BOMId}",
                BomName = request.BOMName,
            });

            var sendMessageCommand = new SendNewMessageCommand(request.Subject, contentInternal, recipient, request.SenderLoginNo, request.SenderName);
            var result = await _mediator.Send(sendMessageCommand, cancellationToken);

            var contentExternal = await _mediator.Send(new NoBidHubrfqSendMailTemplateQuery
            {
                HUBRFQStatus = request.HUBRFQStatus,
                Code = request.Code,
                ClientCurrencyCode = request.ClientCurrencyCode,
                BomId = request.BOMId,
                LoginId = request.LoginId,
                ClientId = request.ClientId,
                IsNoBidAll = true,
                IsPoHub = request.IsPoHub,
            }, cancellationToken);

            var loginPreferenceResponse = await _mediator.Send(new LoginPreferenceDetailsCommand { LoginNo = request.SenderLoginNo }, cancellationToken);
            var toEmail = getUserResponse.Data?.EMail;
            var sendEmail = loginPreferenceResponse.Data?.SendEmail;
            if (sendEmail is true && !string.IsNullOrWhiteSpace(toEmail))
            {
                await _emailService.TrySendEmailAsync(request.SenderEmail!, [toEmail], [], [], request.Subject, contentExternal.Data ?? "", [], [], cancellationToken);
            }
            return result;
        }
    }
}

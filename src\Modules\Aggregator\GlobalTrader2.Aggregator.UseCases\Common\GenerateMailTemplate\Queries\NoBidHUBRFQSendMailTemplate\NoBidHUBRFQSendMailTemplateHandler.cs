using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;

namespace GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate
{
    public class NoBidHubrfqSendMailTemplateHandler(IMediator mediator, IRazorViewToStringService razorViewToStringService) : IRequestHandler<NoBidHubrfqSendMailTemplateQuery, BaseResponse<string>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IRazorViewToStringService _razorViewToStringService = razorViewToStringService;

        public async Task<BaseResponse<string>> Handle(NoBidHubrfqSendMailTemplateQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<string>();

            var bom = await _mediator.Send(new GetBOMDetailsQuery() { Id = request.BomId, LoginId = request.LoginId }, cancellationToken);

            var reqs = await _mediator.Send(new GetBOMListForCustomerRequirementQuery() { BOMNo = request.BomId, ClientID = request.ClientId, IsPoHub = false }, cancellationToken);

            var templateModel = new ReleasedHUBRFQMailTemplate
            {
                HUBRFQStatus = request.HUBRFQStatus,
                Code = request.Code,
                Name = bom?.Data?.Name ?? "",
                Contact = bom?.Data?.ContactName ?? "",
                Company = bom?.Data?.Company ?? "",
                Currency = bom?.Data?.CurrencyCode ?? "",
                QuoteRequired = bom?.Data?.QuoteRequired ?? DateTime.Now,
                ClientCurrencyCode = request.ClientCurrencyCode,
                IsAllNoBid = request.IsNoBidAll,
                IsPoHub = request.IsPoHub,
                Requirements = [.. reqs.Data],
            };

            var contentExternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/ReleasedHUBRFQMail", templateModel);

            response.Data = contentExternal;
            response.Success = true;

            return response;

        }
    }
}

﻿using AutoFixture;
using AutoFixture.AutoMoq;
using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate;
using GlobalTrader2.Aggregator.UseCases.Common.NotificationAndSendEmail.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.CustomerRequirementNoBidFlow.Commands;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CustomerRequirementNoBid.Commands;
using MediatR;
using Moq;

namespace GlobalTrader2.Aggregator.Test.Orders.BOM.CustomerRequirementNoBidFlow.Commands
{
    public class UpdateCustomerRequirementNoBidFlowHandlerTests
    {
        private readonly UpdateCustomerRequirementNoBidFlowHandler _handler;
        private readonly UpdateCustomerRequirementNoBidFlowCommand _request;
        private readonly IFixture _fixture;
        private readonly Mock<IMediator> _mediator;

        public UpdateCustomerRequirementNoBidFlowHandlerTests()
        {
            _fixture = new Fixture();

            _mediator = new Mock<IMediator>();
            _handler = new UpdateCustomerRequirementNoBidFlowHandler(_mediator.Object);
            _request = _fixture.Create<UpdateCustomerRequirementNoBidFlowCommand>();
        }

        [Fact]
        public async Task Handle_WhenCustomerRequirementNoBidFlowIdIsReturned_ShouldReturnSuccessResponse()
        {
            // Arrange
            _mediator.Setup(x => x.Send(It.IsAny<UpdateCustomerRequirementNoBidCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<bool>() { Success = true });
            _mediator.Setup(x => x.Send(It.IsAny<NoBidHubrfqSendMailTemplateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string>() { Success = true, Data = "Test email content" });
            _mediator.Setup(x => x.Send(It.IsAny<CreateNotificationAndSendEmailCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<int>() { Success = true, Data = 1 });

            // Act
            var result = await _handler.Handle(_request, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
        }
    }
}

using GlobalTrader2.Aggregator.UseCases.Helper;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.GetListForBOMSourcingResult.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingLog;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingResultQuotes;

namespace GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate
{
    public class ReleaseHubrfqSendMailTemplateHandler(IMediator mediator, IRazorViewToStringService razorViewToStringService, IMapper mapper) : IRequestHandler<ReleaseHubrfqSendMailTemplateQuery, BaseResponse<string>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IRazorViewToStringService _razorViewToStringService = razorViewToStringService;
        private readonly IMapper _mapper = mapper;

        public async Task<BaseResponse<string>> Handle(ReleaseHubrfqSendMailTemplateQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<string>();

            var bom = await _mediator.Send(new GetBOMDetailsQuery() { Id = request.BomId, LoginId = request.LoginId }, cancellationToken);

            var reqs = await _mediator.Send(new GetBOMListForCustomerRequirementQuery() { BOMNo = request.BomId, ClientID = request.ClientId, IsPoHub = false }, cancellationToken);

            IList<ListForBomSourcingResultDto> sourcingData;

            if (request.IsReleasedAll)
            {
                var sourcingList = await _mediator.Send(new GetListForBomSourcingResultQuery
                {
                    BOMNo = request.BomId,
                    IsPoHub = request.IsPoHub
                }, cancellationToken);

                sourcingData = sourcingList.Data ?? [];
            }
            else
            {
                var sourcingList = await _mediator.Send(new GetListForBOMCustomerRequirementQuery
                {
                    ClientId = request.ClientId,
                    CustomerRequirementId = request.RequirementId ?? 0,
                    IsPOHub = request.IsPoHub,
                    ClientCurrencyCode = request.ClientCurrencyCode,
                    CultureInfo = request.CultureInfo,
                    LoginId = request.LoginId,
                    ClientCurrencyID = request.ClientCurrencyId,
                }, cancellationToken);

                sourcingData = _mapper.Map<List<ListForBomSourcingResultDto>>(sourcingList.Data ?? []);
            }

            var enhancedSourcingResults = await EnhanceSourcingResultsAsync(sourcingData, cancellationToken);

            var templateModel = new ReleasedHUBRFQMailTemplate
            {
                HUBRFQStatus = request.HUBRFQStatus,
                Code = request.Code,
                Name = bom?.Data?.Name ?? "",
                Contact = bom?.Data?.ContactName ?? "",
                Company = bom?.Data?.Company ?? "",
                Currency = bom?.Data?.CurrencyCode ?? "",
                QuoteRequired = bom?.Data?.QuoteRequired ?? DateTime.Now,
                ClientCurrencyCode = request.ClientCurrencyCode,
                Requirements = [.. reqs.Data],
                IsAllReleased = request.IsReleasedAll,
                IsPoHub = request.IsPoHub,
                SourcingResults = enhancedSourcingResults
            };

            var contentExternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/ReleasedHUBRFQMail", templateModel);

            response.Data = contentExternal;
            response.Success = true;

            return response;

        }

        private async Task<List<EnhancedListForBomSourcingResultDto>> EnhanceSourcingResultsAsync(
            IList<ListForBomSourcingResultDto> sourcingResults,
            CancellationToken cancellationToken)
        {
            var enhancedResults = new List<EnhancedListForBomSourcingResultDto>();

            foreach (var sourcingResult in sourcingResults)
            {
                var enhanced = await EnhanceSingleSourcingResultAsync(sourcingResult, cancellationToken);
                enhancedResults.Add(enhanced);
            }

            return enhancedResults;
        }

        private async Task<EnhancedListForBomSourcingResultDto> EnhanceSingleSourcingResultAsync(
            ListForBomSourcingResultDto sourcingResult,
            CancellationToken cancellationToken)
        {
            var enhanced = _mapper.Map<EnhancedListForBomSourcingResultDto>(sourcingResult);

            var quotesResponse = await _mediator.Send(new GetSourcingResultQuotesQuery { SourcingResultId = sourcingResult.SourcingResultId }, cancellationToken);
            if (quotesResponse.Success && quotesResponse.Data != null)
            {
                enhanced.QuoteNumbers = [.. quotesResponse.Data.Select(q => q.QuoteNumber.ToString()).Where(qn => !string.IsNullOrEmpty(qn))];
            }

            await SetHighlightingPropertiesAsync(enhanced, sourcingResult, cancellationToken);

            enhanced.ConvertedPriceInBaseCurrency = await _mediator.ConvertValueToBaseCurrencyAsync(sourcingResult.Price, sourcingResult.CurrencyNo, DateTime.Now);
            return enhanced;
        }

        private async Task SetHighlightingPropertiesAsync(EnhancedListForBomSourcingResultDto enhanced, ListForBomSourcingResultDto source, CancellationToken cancellationToken)
        {
            enhanced.HighlightPartOrNotes = false;
            enhanced.HighlightManufacturer = false;
            enhanced.HighlightProductOrPackage = false;
            enhanced.HighlightQuantityOrDeliveryDate = false;
            enhanced.HighlightUpliftPrice = false;
            enhanced.HighlightRegion = false;
            enhanced.HighlightEstimatedShippingCost = false;

            if (source.ReReleased == 1)
            {
                var sourcingLogQuery = new GetSourcingLogQuery { SourcingResultId = source.SourcingResultId };
                var sourcingLogResponse = await _mediator.Send(sourcingLogQuery, cancellationToken);

                if (sourcingLogResponse.Success && sourcingLogResponse.Data != null)
                {
                    var sourcingLog = sourcingLogResponse.Data.FirstOrDefault();
                    if (sourcingLog?.ChangeNotes != null)
                    {
                        var changeNotes = sourcingLog.ChangeNotes.Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(note => note.Trim())
                            .ToList();

                        enhanced.HighlightPartOrNotes = changeNotes.Any(note =>
                            note.Equals("PartNo", StringComparison.OrdinalIgnoreCase) ||
                            note.Equals("Notes", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightManufacturer = changeNotes.Any(note =>
                            note.Equals("Manufacturer", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightProductOrPackage = changeNotes.Any(note =>
                            note.Equals("Product", StringComparison.OrdinalIgnoreCase) ||
                            note.Equals("Package", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightQuantityOrDeliveryDate = changeNotes.Any(note =>
                            note.Equals("Quantity", StringComparison.OrdinalIgnoreCase) ||
                            note.Equals("DeliveryDate", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightUpliftPrice = changeNotes.Any(note =>
                            note.Equals("UpliftPrice", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightRegion = changeNotes.Any(note =>
                            note.Equals("Region", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightEstimatedShippingCost = changeNotes.Any(note =>
                            note.Equals("EstimatedShippingCost", StringComparison.OrdinalIgnoreCase));
                    }
                }
            }
        }
    }
}

using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.Dtos;
using MediatR;
using Moq;

namespace GlobalTrader2.Aggregator.Test.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate
{
    public class NoBidHUBRFQSendMailTemplateHandlerTest
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<IRazorViewToStringService> _razorViewToStringServiceMock;
        private readonly NoBidHubrfqSendMailTemplateHandler _handler;
        private readonly IFixture _fixture;

        public NoBidHUBRFQSendMailTemplateHandlerTest()
        {
            _fixture = new Fixture();
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mediatorMock = new Mock<IMediator>();
            _razorViewToStringServiceMock = new Mock<IRazorViewToStringService>();

            _handler = new NoBidHubrfqSendMailTemplateHandler(
                _mediatorMock.Object,
                _razorViewToStringServiceMock.Object);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldReturnSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldCallGetBOMDetailsQuery()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetBOMDetailsQuery>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldCallGetBOMListForCustomerRequirementQuery()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _mediatorMock.Verify(m => m.Send(
                It.IsAny<GetBOMListForCustomerRequirementQuery>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldCallRazorViewService()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupBasicMockResponses();

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _razorViewToStringServiceMock.Verify(r => r.RenderViewToStringAsync(
                It.IsAny<string>(),
                It.IsAny<object>()), Times.Once);
        }

        private NoBidHubrfqSendMailTemplateQuery CreateValidRequest()
        {
            return new NoBidHubrfqSendMailTemplateQuery
            {
                BomId = 123,
                LoginId = 456,
                ClientId = 789,
                HUBRFQStatus = "Active",
                Code = "BOM001",
                ClientCurrencyCode = "USD",
                IsNoBidAll = true,
                IsPoHub = false,
                RequirementId = 101
            };
        }

        private void SetupBasicMockResponses()
        {
            // Setup GetBOMDetailsQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetBOMDetailsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<BOMDetailsDto>
                {
                    Success = true,
                    Data = _fixture.Create<BOMDetailsDto>()
                });

            // Setup GetBOMListForCustomerRequirementQuery response
            _mediatorMock.Setup(m => m.Send(It.IsAny<GetBOMListForCustomerRequirementQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IList<BOMListForCustomerRequirementDto>>
                {
                    Success = true,
                    Data = _fixture.CreateMany<BOMListForCustomerRequirementDto>(2).ToList()
                });

            // Setup razor view service
            _razorViewToStringServiceMock.Setup(r => r.RenderViewToStringAsync(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync("Rendered email content");
        }
    }
}

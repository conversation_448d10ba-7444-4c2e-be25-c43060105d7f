//-----------------------------------------------------------------------------------------
// RP 24.05.2010:
// - send error message back from check database
//Marker     Changed by      Date         Remarks
//[001]      Vinay           04/07/2012   This need to notify the user by email.
//[002]      Vinay           17/06/2013   CR:- Supplier Invoice
//[003]      Vinay           03/09/2013   NPR Report
//[004]      Shashi Keshar   23/09/2016  MailTemplate for Send to Purchase Hub from HUBRFQ and Requirement Main Info Page 
//[005]      Shashi Keshar   06/10/2016  PO Approved & Un Approved Mail Message functionality changes
//[006]      Shashi Keshar   16/11/2016  Created Method to Get Email ID for CC in Send to Purchase Hub 
//[007]      <PERSON><PERSON><PERSON> & <PERSON><PERSON> 15/07/2021 Added method for send email and notification of short shipment
//[007]      Abhinav <PERSON>  15/07/2021  Add new method for partwatch Match.
//[008]      Abhinav <PERSON>a  10-08-2021  Fix issues for nofity.
//[007]      Ab<PERSON>av <PERSON>xena  01/09/2021  Add new mailer function for supplier approval.
//-----------------------------------------------------------------------------------------
using iTextSharp.text;
using iTextSharp.text.pdf;
using Microsoft.Azure.Storage.Blob;
using Newtonsoft.Json;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Mail;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Script.Services;
using System.Web.Services;

namespace Rebound.GlobalTrader.Site
{
    /// <summary>
    /// Summary description for WebServices
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [ScriptService()]
    [ToolboxItem(false)]
    public class WebServices : System.Web.Services.WebService
    {
        /// <summary>
        /// Logs user out of the system.
        /// </summary>
        [WebMethod(EnableSession = true)]
        public void Logout()
        {
            LoginManager.LogUserOut();
        }
        /// <summary>
        /// Checks if a user is logged into the system.
        /// </summary>
        [WebMethod(EnableSession = true)]
        public bool CheckLoggedIn()
        {
            SessionManager.MaintainStateWithoutFullPostback();
            return SessionManager.CheckLoggedIn();
        }

        /// <summary>
        /// Adds a page to the recently viewed list
        /// </summary>
        [WebMethod()]
        public string AddToRecentlyViewedList(string strID, string strURL, string strTitle)
        {
            Controls.LeftNuggets.RecentlyViewed.AddToList(strURL, strTitle);
            return RefreshRecentlyViewedList(strID);
        }

        /// <summary>
        /// Refresh the recently viewed list
        /// </summary>
        [WebMethod()]
        public string RefreshRecentlyViewedList(string strID)
        {
            return Controls.LeftNuggets.RecentlyViewed.RenderPageHistoryForAjax(strID);
        }

        /// <summary>
        /// Lock a recently viewed item
        /// </summary>
        [WebMethod()]
        public int LockRecentlyViewedItem(int intIndex, int intRecentlyViewedID, bool blnLock)
        {
            BLL.RecentlyViewed.UpdateLock(intRecentlyViewedID, blnLock);
            return intIndex;
        }

        /// <summary>
        /// Works out Landed Cost for an InvoiceLineAllocation
        ///	Calculation: 
        ///(Qty x  Unit Cost x Currency Exchange Rate x Duty Rate of Product) Plus Shipping Cost all Divided by Qty.
        /// </summary>
        [WebMethod(EnableSession = true)]
        public string CalculateLandedcost(int intQuantity, double dblCost, int intFunds, string strDate, bool blnApplyDuty, int intProductID, double dblShippingCost)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            double? dblResult = 0;
            //parse string date into a date object
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            DateTime dtmDate = Convert.ToDateTime(strDate, ci);
            Product pr = BLL.Product.GetLandedCostCalculation(intQuantity, dtmDate, intFunds, dblCost, dblShippingCost, blnApplyDuty, intProductID);
            if (pr != null) dblResult = pr.LandedCost;
            return Functions.FormatCurrency(dblResult);
        }

        [WebMethod(EnableSession = true)]
        public string CheckDatabaseConnection()
        {
            string strError = "";
            try
            {
                BLL.CommunicationLogType.Count();
            }
            catch (Exception ex)
            {
                strError = ex.Message;
                if (ex.InnerException != null) strError = string.Format("{0}\n\r{1}", strError, ex.InnerException.Message);
            }
            return strError;
        }

        /// <summary>
        /// For Rebound Admin Login, complete Client choice
        /// </summary>
        [WebMethod(EnableSession = true)]
        public string DoReboundClientChoice(int intClientID, string strRedirectPath)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            string strReturn = "";
            //check for Rebound Admin User ID
            if (SessionManager.LoginID == 0)
            {
                BLL.Login lg = BLL.Login.GetForBoris(intClientID);
                SessionManager.StoreClientLoginItems(lg);
                lg = null;

                //setup return of redirect path 
                if (!String.IsNullOrEmpty(strRedirectPath)) strReturn = strRedirectPath;
                if (strReturn == "") strReturn = Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Home").Url;
                strReturn = strReturn.Replace("~/", "");
                if (strReturn.StartsWith("/")) strReturn = strReturn.Remove(0, 1);
            }
            return strReturn;
        }

        /// <summary>
        /// Clears the state of a DataListNugget
        /// </summary>
        [WebMethod(EnableSession = true)]
        public void ClearDataListNuggetState(int intDataListNuggetID, string strSubType)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            BLL.DataListNuggetState.DeleteForDLNAndLogin(intDataListNuggetID, strSubType, SessionManager.LoginID);
        }

        /// <summary>
        /// Sets the session variable for the left panel visibility
        /// </summary>
        /// <param name="strShow">'true' or 'false'</param>
        [WebMethod(EnableSession = true)]
        public void SetLeftPanelVisibleSession(bool blnShow)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            SessionManager.LeftPanelVisible = blnShow;
        }

        /// <summary>
        /// Sets language
        /// </summary>
        /// <param name="strShow">'true' or 'false'</param>
        [WebMethod(EnableSession = true)]
        public void SetCulture(string strCulture)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            SessionManager.Culture = strCulture;
        }

        #region Message functions

        /// <summary>
        /// Check for mail messages and To Do alerts
        /// </summary>
        [WebMethod(EnableSession = true)]
        public string CheckForMessages()
        {
            SessionManager.MaintainStateWithoutFullPostback();
            string strOut = "";
            JsonObject jsn = new JsonObject();
            if (!SessionManager.CheckLoggedIn())
            {
                jsn.AddVariable("LOGGED_OUT", true);
            }
            else
            {
                //get messages
                JsonObject jsnMessages = new JsonObject(true);
                foreach (Rebound.GlobalTrader.BLL.MailMessage msg in Rebound.GlobalTrader.BLL.MailMessage.GetListNewAndUnnotifiedForRecipient(SessionManager.LoginID))
                {
                    JsonObject jsnMessage = new JsonObject();
                    jsnMessage.AddVariable("ID", msg.MailMessageId);
                    jsnMessage.AddVariable("Subject", msg.Subject);
                    jsnMessage.AddVariable("From", msg.FromLoginName);
                    jsnMessage.AddVariable("DateSent", Functions.FormatDate(msg.DateSent, false, true));
                    jsnMessages.AddVariable(jsnMessage);
                }
                jsn.AddVariable("Messages", jsnMessages);

                //get To Do Alerts
                jsn.AddVariable("Alerts", GetJSONForAlerts());
            }
            strOut = jsn.Result;
            return strOut;
        }

        /// <summary>
        /// Check for To Do alerts
        /// </summary>
        [WebMethod()]
        public string CheckForAlerts()
        {
            string strOut = "";
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Alerts", GetJSONForAlerts());
            strOut = jsn.Result;
            return strOut;
        }


        /// <summary>
        /// Gets a JSON object for the To Do Alerts
        /// </summary>
        private JsonObject GetJSONForAlerts()
        {
            //get To Do Alerts
            JsonObject jsnToDos = new JsonObject(true);
            foreach (BLL.ToDo td in BLL.ToDo.GetListAlertForLogin(SessionManager.LoginID, DateTime.Now))
            {
                JsonObject jsnToDo = new JsonObject();
                jsnToDo.AddVariable("ID", td.ToDoId);
                jsnToDo.AddVariable("DueDate", Functions.FormatDate(td.DueDate, false, true));
                jsnToDo.AddVariable("Text", td.ReminderText);
                jsnToDos.AddVariable(jsnToDo);
            }
            return jsnToDos;
        }

        /// <summary>
        /// Dismiss Alert
        /// </summary>
        [WebMethod()]
        public void DismissAlert(int intToDoID)
        {
            BLL.ToDo.UpdateDismiss(intToDoID, SessionManager.LoginID);
        }

        /// <summary>
        /// Snooze Alert
        /// </summary>
        [WebMethod()]
        public void SnoozeAlert(int intToDoID, int intMinutes)
        {
            BLL.ToDo.UpdateSnooze(intToDoID, intMinutes, SessionManager.LoginID);
        }

        [WebMethod()]
        public void SendMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                      SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );
                }
            }
            catch { }
        }
        [WebMethod()]
        public void NotifyMessageExpediteNote(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, bool sendEmailToConfig)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                if (sendEmailToConfig)
                {
                    sendMailIPOPurchasing(strSubject, strMessage);
                }
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        public void GetNameOfCCLoginIDHUBRFQ(string aryRecipientLoginIDsCC, out string addrCC)
        {
            try
            {
                addrCC = null;
                string strEmailTo = "";
                Array aryToLogins = Functions.JavascriptStringToArray(aryRecipientLoginIDsCC);
                List<int> lstToLoginID = new List<int>();
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForEmailName(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        strEmailTo = strEmailTo + "," + objLoginPref.EmployeeName;
                    }
                }
                addrCC = strEmailTo.Substring(1);
            }
            catch (Exception ex)
            {
                addrCC = null;
            }
        }

        public void GetMailIDForCCHUBRFQ(string aryRecipientLoginIDsCC, string strSubject, string strMessage, out MailAddressCollection addrCC)
        {
            try
            {
                addrCC = null;
                MailAddressCollection adrCC = new MailAddressCollection();
                Array aryToLogins = Functions.JavascriptStringToArray(aryRecipientLoginIDsCC);
                List<int> lstToLoginID = new List<int>();
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    bool IsSendEmail = false;
                    string strEmailTo = "";

                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }

                    if (IsSendEmail)
                    {
                        adrCC.Add(strEmailTo);

                    }
                }
                addrCC = adrCC;

            }
            catch (Exception ex)
            {
                addrCC = null;
            }
        }
        [WebMethod()]
        public void NotifyMessageExpediteNoteHUBRFQ2(string strToLoginsArray, string strSubject, string strMessage, bool sendEmailToConfig, string strCCArray, string SendToGroup)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                List<int> lstToLoginID = new List<int>();

                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }
                MailAddressCollection addrCC = null;
                //GetMailIDForCCHUBRFQ(strCCArray, strSubject, strMessage, out addrCC);

                //if (sendEmailToConfig)
                //{
                //    sendMailIPOPurchasing(strSubject, strMessage);
                //}
                bool IsSendEmail = false;

                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );


                    string strEmailTo = "";
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmailCommunicationNote2(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        //IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    string strEmailFrom = SessionManager.LoginEmail;

                    MailAddressCollection adrTo = null;
                    //if (!IsSendEmail && i == 0)
                    //{
                    //    adrTo = new MailAddressCollection();
                    //    System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                    //    System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                    //    bool flag = OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                    //    IsSendEmail = true;
                    //    continue;
                    //}

                    //if (IsSendEmail)
                    //{
                    adrTo = new MailAddressCollection();
                    adrTo.Add(strEmailTo);
                    System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                    System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                    bool flag = OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                    addrCC = null;

                    //}
                }
            }
            catch (Exception ex) { }
            ;
        }

        [WebMethod()]
        public void NotifyMessageExpediteNoteHUBRFQ(string strToLoginsArray, string strSubject, string strMessage, bool sendEmailToConfig, string strCCArray, string SendToGroup)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                List<int> lstToLoginID = new List<int>();

                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }
                MailAddressCollection addrCC = null;
                GetMailIDForCCHUBRFQ(strCCArray, strSubject, strMessage, out addrCC);

                if (sendEmailToConfig)
                {
                    sendMailIPOPurchasing(strSubject, strMessage);
                }

                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmailCommunicationNote(lstToLoginID[i], SendToGroup);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    string strEmailFrom = SessionManager.LoginEmail;

                    MailAddressCollection adrTo = null;
                    if (!IsSendEmail && i == 0)
                    {
                        adrTo = new MailAddressCollection();
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        bool flag = OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        continue;
                    }

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        bool flag = OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        addrCC = null;

                    }
                }
            }
            catch (Exception ex) { }
            ;
        }

        [WebMethod()]
        public void NotifyMessageECCNNotifications(string strToLoginsArray, string strSubject, string strMessage)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                List<int> lstToLoginID = new List<int>();

                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }


                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );
                    string strEmailTo = "";
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        strEmailTo = objLoginPref.Email;
                    }

                    string strEmailFrom = SessionManager.LoginEmail;

                    MailAddressCollection adrTo = null;
                    adrTo = new MailAddressCollection();
                    adrTo.Add(strEmailTo);
                    System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                    //System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                    bool flag = OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, null, true);

                }
            }
            catch (Exception ex)
            {
            }
            ;
        }
        [WebMethod()]
        public void SalesOrderNotifyMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, string strSalesOrderNo, int? SalesOrderId)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
            }
            catch { }
        }

        protected void sendMailIPOPurchasing(string strSubject, string strMessage)
        {
            string IPOPurchasingEmail = "";
            //IPOPurchasingEmail = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["IPOPurchasing"]);
            //Jira Ticket: RS-332 23 Sep 2021
            IPOPurchasingEmail = SettingsManager.GetSetting(SettingItem.List.IPOPurchasing);
            string strEmailFrom = SessionManager.LoginEmail;
            MailAddressCollection adrTo = null;
            adrTo = new MailAddressCollection();
            adrTo.Add(IPOPurchasingEmail);
            System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
            System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

            OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
            // OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);

        }
        [WebMethod()]
        public void NotifyMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);


                    }
                }
            }
            catch { }
        }

        // send Email and PDF attachment when create new invoice
        [WebMethod()]
        public void NotifyMessageNewInvoiceAdd(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, int? intInvoiceID)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        if (intInvoiceID > 0)
                        {
                            GeneratePDF obj = new GeneratePDF();
                            Invoice invPS = Invoice.GetForPrint(intInvoiceID);
                            string strpsPdfPath = "";
                            obj.PrintPackingSlip(invPS, true, out strpsPdfPath);
                            obj = null;
                            OutgoingMailManager.SendUserMailDocumentWithPDF(
                                         strSubject
                                         , adrTo
                                         , adrFrom
                                         , null
                                         , null
                                         , adrReplyTo
                                         , strpsPdfPath
                                         , Convert.ToString(invPS.InvoiceNumber) + ".pdf"
                                         , RemoveHyperLink(strMessage)
                                         );
                        }

                    }
                }
            }
            catch { }
        }
        private string RemoveHyperLink(string strMessage)
        {
            return Regex.Replace(strMessage, "</?(a|A).*?>", "").Replace("\r\n", "<br/>");
        }


        /// <summary>
        /// New customer requirement added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewCustomerRequirement(int intCustomerRequirementID)
        {
            string strOut = "";
            try
            {
                BLL.CustomerRequirement cr = BLL.CustomerRequirement.Get(intCustomerRequirementID);
                if (cr != null) strOut = MailTemplateManager.GetMessage_NewCustomerRequirement(cr);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Quote added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewQuote(int intQuoteID)
        {
            string strOut = "";
            try
            {
                BLL.Quote qt = BLL.Quote.Get(intQuoteID);
                if (qt != null) strOut = MailTemplateManager.GetMessage_NewQuote(qt);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Supplier RMA added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewSupplierRMA(int intSRMAID)
        {
            string strOut = "";
            try
            {
                BLL.SupplierRma srma = BLL.SupplierRma.Get(intSRMAID);
                if (srma != null) strOut = MailTemplateManager.GetMessage_NewSupplierRMA(srma);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Customer RMA added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewCustomerRMA(int intCRMAID)
        {
            string strOut = "";
            try
            {
                BLL.CustomerRma crma = BLL.CustomerRma.Get(intCRMAID);
                if (crma != null) strOut = MailTemplateManager.GetMessage_NewCustomerRMA(crma);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Credit Note added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewCreditNote(int intCreditID)
        {
            string strOut = "";
            try
            {
                BLL.Credit credit = BLL.Credit.Get(intCreditID);
                if (credit != null) strOut = MailTemplateManager.GetMessage_NewCredit(credit);
            }
            catch { }
            return strOut;
        }
        /// <summary>
        /// New Debit Note added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewDebitNote(int intDebitID)
        {
            string strOut = "";
            try
            {
                BLL.Debit debit = BLL.Debit.Get(intDebitID);
                if (debit != null) strOut = MailTemplateManager.GetMessage_NewDebit(debit);
            }
            catch { }
            return strOut;
        }
        /// <summary>
        /// New Goods In added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewGoodsIn(int intGoodsInID)
        {
            string strOut = "";
            try
            {
                BLL.GoodsIn gi = BLL.GoodsIn.Get(intGoodsInID, SessionManager.IsPOHub);
                if (gi != null) strOut = MailTemplateManager.GetMessage_NewGoodsIn(gi);
            }
            catch { }
            return strOut;
        }
        /// <summary>
        /// Received PO
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_ReceivedPurchaseOrder(int intGoodsInID)
        {
            string strOut = "";
            try
            {
                BLL.GoodsIn gi = BLL.GoodsIn.Get(intGoodsInID, SessionManager.IsPOHub);
                if (gi != null) strOut = MailTemplateManager.GetMessage_ReceivedPurchaseOrder(gi);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Sales Order added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewSalesOrder(int intSOID)
        {
            string strOut = "";
            try
            {
                BLL.SalesOrder so = BLL.SalesOrder.Get(intSOID);
                if (so != null) strOut = MailTemplateManager.GetMessage_NewSalesOrder(so);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// Notify Sales Order 
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NotifySalesOrder(int intSOID)
        {
            string strOut = "";
            try
            {
                BLL.SalesOrder so = BLL.SalesOrder.Get(intSOID);
                if (so != null) strOut = MailTemplateManager.GetMessage_NotifySalesOrder(so);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// Notify Purchase Order 
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NotifyPurchaseOrder(int intPOID)
        {
            string strOut = "";
            try
            {
                BLL.PurchaseOrder po = BLL.PurchaseOrder.Get(SessionManager.ClientID, intPOID);
                if (po != null) strOut = MailTemplateManager.GetMessage_NotifyPurchaseOrder(po);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// Notify Goods In
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NotifyGoodsIn(int intGoodsInID)
        {
            string strOut = "";
            try
            {
                BLL.GoodsIn gi = BLL.GoodsIn.Get(intGoodsInID, SessionManager.IsPOHub);
                if (gi != null) strOut = MailTemplateManager.GetMessage_NotifyGoodsIn(gi);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Purchase Order added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewPurchaseOrder(int intPOID)
        {
            string strOut = "";
            try
            {
                BLL.PurchaseOrder po = BLL.PurchaseOrder.Get(SessionManager.ClientID, intPOID);
                if (po != null) strOut = MailTemplateManager.GetMessage_NewPurchaseOrder(po);
            }
            catch { }
            return strOut;
        }

        /// <summary>
        /// New Invoice added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewInvoice(int intInvoiceID)
        {
            string strOut = "";
            try
            {
                //string strpsPdfPath = "";
                BLL.Invoice inv = BLL.Invoice.Get(intInvoiceID);
                strOut = MailTemplateManager.GetMessage_NewInvoice(inv);
                //GeneratePDF obj = new GeneratePDF();
                //if (inv != null)
                //{ 
                //    obj.PrintPackingSlip(inv, true, out strpsPdfPath);  
                //    obj = null;
                //    strOut = OutgoingMailManager.SendUserMailDocumentWithPDF(
                //                       GetFormValue_ForEmail("Subject")
                //                       , adTo
                //                       , adFrom
                //                       , null
                //                       , adBCC
                //                       , adReplyTo
                //                       , strpsPdfPath
                //                       , Convert.ToString(inv.InvoiceNumber) + ".pdf"
                //                       , MailTemplateManager.GetMessage_NewInvoice(inv)
                //                       );

                //}
            }
            catch { }
            return strOut;
        }

        //[002] code start

        /// <summary>
        /// New Supplier Invoice added
        /// </summary>
        [WebMethod()]
        public string GetMailMessage_NewSupplierInvoice(int intSIID)
        {
            string strOut = "";
            try
            {
                BLL.SupplierInvoice si = BLL.SupplierInvoice.Get(intSIID);
                if (si != null) strOut = MailTemplateManager.GetMessage_NewSupplierInvoice(si);
            }
            catch { }
            return strOut;
        }
        [WebMethod()]
        public string GetMailMessage_NotifySupplierInvoice(int intSupplierInvoiceID)
        {
            string strOut = "";
            try
            {
                BLL.SupplierInvoice si = BLL.SupplierInvoice.Get(intSupplierInvoiceID);
                if (si != null) strOut = MailTemplateManager.GetMessage_NotifySupplierInvoice(si);
            }
            catch { }
            return strOut;
        }
        [WebMethod()]
        public string GetMailMessage_CompanyPO(int intCompanyID)
        {
            string strOut = "";
            try
            {
                BLL.Company cmp = BLL.Company.GetPurchaseInfo(intCompanyID);
                if (cmp != null) strOut = MailTemplateManager.GetMessage_POCompanyApproved(cmp);
            }
            catch { }
            return strOut;
        }
        [WebMethod()]
        public string GetMailMessage_CompanySO(int intCompanyID)
        {
            string strOut = "";
            try
            {
                BLL.Company cmp = BLL.Company.GetSalesInfo(intCompanyID);
                if (cmp != null) strOut = MailTemplateManager.GetMessage_SOCompanyApproved(cmp);
            }
            catch { }
            return strOut;
        }
        //[002] code end

        ////[003] code start
        ///// <summary>
        ///// Notify NPR
        ///// </summary>
        //[WebMethod()]
        //public string GetMailMessage_NotifyNPR(int intNPRID)
        //{
        //      
        //    try
        //    {
        //        BLL.ReportNPR npr = BLL.ReportNPR.GetNPRById(intNPRID);
        //        if (npr != null) strOut = MailTemplateManager.GetMessage_NotifyNPR(npr);
        //    }
        //    catch { }
        //    return strOut;
        //}
        ////[003] code start
        [WebMethod()]
        public void NotifyNPRMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, int nprId, int goodsInLineId, string strLoginName, string strGroupName)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;

                string strMessageTemp = string.Format("Please review this <a onclick=\"{0}\" href=\"javascript:void(0);\">NPR</a>:", string.Format("$RGT_openNPRWindow({0} ,{1})", goodsInLineId, nprId));
                string strEmailName = strLoginName + "/" + strGroupName;

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                strMessage = strMessageTemp + "<br/><br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, this.ChangeInOutlookForm(strMessage, nprId, goodsInLineId), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
                //save npr log

                ReportNPR.InsertEmailNPRLog(nprId, strNPREmailLog, SessionManager.LoginID);
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyEPRMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, int eprId, int POId, string strLoginName, string strGroupName, int PONumber)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;

                string strMessageTemp = string.Format("Please review this <a onclick=\"{0}\" href=\"javascript:void(0);\">EPR</a>:", string.Format("$RGT_openEPRWindowEmail({0} ,{1})", POId, eprId));

                string strTempPON = string.Format("Purchase Order: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", "Ord_PODetail.aspx?po=" + POId, PONumber);
                string strTempPOE = string.Format("Purchase Order: {0}", PONumber);

                string strEmailName = strLoginName + "/" + strGroupName;

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                string strMessageE = strMessageTemp + "<br/><br/>" + strTempPOE + "<br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;
                strMessage = strMessageTemp + "<br/><br/>" + strTempPON + "<br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;


                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        // Check Inactive user before send as per client request
                        if (mgm.Inactive == false)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                        }
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        string poLink = "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "Ord_PODetail.aspx?po=" + POId + "'>" + PONumber + "</a>";
                        strMessageE = this.ChangeInOutlookEPRForm(strMessageE);
                        strMessageE = strMessageE.Replace(PONumber.ToString(), poLink);

                        OutgoingMailManager.SendUserMail(strSubject, strMessageE, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
                //save npr log
                EPR.InsertEmailEPRLog(eprId, strNPREmailLog, SessionManager.LoginID);
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyEPRRejectedMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, int eprId, int POId, string strLoginName, string strGroupName, int PONumber)
        {
            string EPRRejectedMessage = strMessage;
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;

                string strMessageTemp = string.Format("Please review this <a onclick=\"{0}\" href=\"javascript:void(0);\">EPR</a>:", string.Format("$RGT_openEPRWindowEmail({0} ,{1})", POId, eprId));

                string strTempPON = string.Format("Purchase Order: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", "Ord_PODetail.aspx?po=" + POId, PONumber);
                string strTempPOE = string.Format("Purchase Order: {0}", PONumber);
                string strEPRNo = string.Format("EPR : {0}", PONumber + "-" + eprId);

                string strEmailName = strLoginName + "/" + strGroupName;

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                string strMessageE = strMessageTemp + "<br/><br/>" + strTempPOE + "<br/>" + strEPRNo + "<br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;
                strMessage = strMessageTemp + "<br/><br/>" + strTempPON + "<br/>" + strEPRNo + "<br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;


                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        // Check Inactive user before send as per client request
                        if (mgm.Inactive == false)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                        }
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    if (strMessage.Contains("EPRREJECTED"))
                    {
                        strMessage = strMessage.Replace("EPRREJECTED||", "Reason: ");
                    }

                    BLL.MailMessage.Insert(
                    SessionManager.LoginID
                , lstToLoginID[i]
                , strSubject
                , strMessage
                , intCompanyNo
                , SessionManager.LoginID
                );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        string eLink = "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "Ord_PODetail.aspx?po=" + POId + "'>" + PONumber + "</a>";
                        strMessageE = this.ChangeInOutlookEPRForm(strMessageE);
                        if (strMessageE.Contains("EPRREJECTED"))
                        {
                            strMessageE = strMessageE.Replace("EPRREJECTED||", "Reason: ");
                            strMessageE = strMessageE.Replace(PONumber.ToString(), eLink);
                        }
                        OutgoingMailManager.SendUserMail(strSubject, strMessageE, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }

                EPR.InsertEmailEPRLog(eprId, EPRRejectedMessage, SessionManager.LoginID);
            }
            catch { }
        }

        private string ChangeInOutlookForm(string strMessage, int nprId, int goodsInLineId)
        {
            //string strUrl = PageManager.GotoURL_NPR(goodsInLineId, nprId);
            return Regex.Replace(strMessage, "<a [^>]+>(.*?)</a>", "NPR").Replace("\n", "<br/>");
        }
        private string ChangeInOutlookEPRForm(string strMessage)
        {
            //string strUrl = PageManager.GotoURL_NPR(goodsInLineId, nprId);
            return Regex.Replace(strMessage, "<a [^>]+>(.*?)</a>", "EPR").Replace("\n", "<br/>");
        }

        [WebMethod(EnableSession = true)]
        public string CalculateStockProvision(double dblCost, int stockprovision)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            double? dblResult = dblCost;
            double? calculated = (dblCost * stockprovision) / 100;
            if (calculated > 0)
                dblResult = (dblResult - calculated);
            return Functions.FormatCurrency(dblResult);
        }

        [WebMethod(EnableSession = true)]
        public string CalculateLotStockProvision(double dblCost, double stockprovision, int stockProvisiontype)
        {
            SessionManager.MaintainStateWithoutFullPostback();
            double? dblResult = dblCost;
            if (stockProvisiontype == 0)
            {
                double? calculated = (dblCost * stockprovision) / 100;
                if (calculated > 0)
                    dblResult = (dblResult - calculated);
            }
            if (stockProvisiontype == 1)
            {

                double? calculated = ((stockprovision * 100) / dblCost);
                if (calculated > 0)
                    dblResult = (100 - calculated);
            }
            return Functions.FormatCurrency(dblResult);
        }

        [WebMethod()]
        public void CreateIPONotify(string strToLoginsArray, string strToGroupsArray, string strSubject, int ipo, int po, int? intCompanyNo, BLL.PurchaseOrder _po, bool isPO)
        {
            try
            {
                //BLL.PurchaseOrder _po = BLL.PurchaseOrder.Get(po);
                //_po.InternalPurchaseOrderNo = ipo;
                string strMessage = string.Empty;
                if (isPO)
                {
                    // _po.InternalPurchaseOrderNo = ipo;
                    strMessage = MailTemplateManager.GetMessage_NotifyPOTOCreateIPO(_po);
                }
                else
                {
                    strMessage = MailTemplateManager.GetMessage_NotifyCreateIPO(_po);
                }
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void POApprovedNotify(string strSubject, int id)
        {
            try
            {
                // string IPOPurchasingEmail = "";
                // IPOPurchasingEmail = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["IPOPurchasing"]);

                BLL.PurchaseOrder _po = BLL.PurchaseOrder.GetIPONotification(id);

                if (_po != null)
                {
                    string strMessage = MailTemplateManager.GetMessage_NotifyPOApprovedNew(_po);

                    //[005]  Start Here
                    string strToLoginsArray = "";
                    if (_po.SupportTeamMemberNo > 0)
                    {
                        strToLoginsArray = Convert.ToString(_po.Buyer) + "||" + Convert.ToString(_po.IPOBuyer) + "||" + Convert.ToString(_po.SupportTeamMemberNo);
                    }
                    else
                    {
                        strToLoginsArray = Convert.ToString(_po.Buyer) + "||" + Convert.ToString(_po.IPOBuyer);
                    }

                    Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);

                    List<int> lstToLoginID = new List<int>();

                    for (int i = 0; i < aryToLogins.Length; i++)
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }

                    for (int i = 0; i < lstToLoginID.Count; i++)
                    {

                        //[005] End Here

                        BLL.MailMessage.Insert(
                                SessionManager.LoginID
                            , lstToLoginID[i]
                            , strSubject
                            , strMessage
                            , null
                            , SessionManager.LoginID
                            );

                        // do email to their external email-id
                        bool IsSendEmail = false;
                        string strEmailTo = "";
                        //[001] code start
                        LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                        if (objLoginPref != null)
                        {
                            IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                            strEmailTo = objLoginPref.Email;
                        }
                        //[001] code end
                        string strEmailFrom = SessionManager.LoginEmail;
                        MailAddressCollection adrTo = null;

                        if (IsSendEmail)
                        {
                            adrTo = new MailAddressCollection();
                            adrTo.Add(strEmailTo);
                            //adrTo.Add(IPOPurchasingEmail);
                            System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                            System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                            OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        }
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyMessagePRSupplier(string strToLoginsArray, string strSubject, string strMessage, int reportNo, int? Id, int? CompanyId, string strToCompanyIdsArray, string CurrencyCode, string strsupplierNamesArray)
        {

            try
            {
                string EmailID = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SendToSupplier"]);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray); //Email ids                           
                MailAddressCollection adrTo = null;
                MailAddressCollection adrCC = new MailAddressCollection();
                System.Net.Mail.MailAddress adrFrom = new MailAddress(SessionManager.LoginEmail);
                System.Net.Mail.MailAddress ReplyTo = new MailAddress(EmailID);
                System.Net.Mail.MailAddress ReplyCC = new MailAddress(SessionManager.LoginEmail);
                adrCC.Add(ReplyCC);

                string Suppliers = "";
                Suppliers = strsupplierNamesArray.Replace("||", ",");
                Array aryToCompanyIds = Functions.JavascriptStringToArray(strToCompanyIdsArray);//Company Id's
                Array arySupplierNames = Functions.JavascriptStringToArray(strsupplierNamesArray);//Supplier Name

                bool isSentSuccessfully = false;
                string sectionName = reportNo == (int)Report.List.RequirementWithBOM ? "BOM" : "PurchaseHub";
                string strTitle = reportNo == (int)Report.List.RequirementWithBOM ? "HUBRFQ" : "Price Request";
                BLL.CSVExportLog.InsertMailLog(Suppliers, Id, sectionName);
                Int32 srNo = 0;
                for (int i = 0; i < aryToCompanyIds.Length; i++)
                {

                    srNo = i + 1;
                    string csvFile = string.Empty;
                    adrTo = new MailAddressCollection();
                    if (((string[])(aryToLogins))[i] != "")
                    {
                        csvFile = FileUploadManager.ExportToCSVForClientListPR(reportNo, Id, CurrencyCode, Convert.ToInt32(aryToCompanyIds.GetValue(i)), srNo);
                        BLL.CSVExportLog.Insert(
                                     Convert.ToInt32(aryToCompanyIds.GetValue(i))
                                     , csvFile
                                     , SessionManager.LoginID
                                     , sectionName
                                     , aryToLogins.GetValue(i).ToString()
                                     , Id
                                     , strSubject
                                     , strMessage
                                 );


                        adrTo.Add(aryToLogins.GetValue(i).ToString());

                        string bodyMessage = OutgoingMailManager.BodyMailMessage(strMessage, arySupplierNames.GetValue(i).ToString());

                        isSentSuccessfully = OutgoingMailManager.SendAttachedDocumentToUsers(strSubject, adrTo, adrFrom, adrCC, null, ReplyTo, bodyMessage, csvFile, null);
                        adrTo = null;
                    }
                }
            }

            catch (Exception) { }
        }

        [WebMethod()]
        public void NotifyMessageSupplier(string strToLoginsArray, string strSubject, string strMessage, int reportNo, int? Id, int? CompanyId, string strToCompanyIdsArray, string CurrencyCode, string strsupplierNamesArray)
        {

            try
            {
                string EmailID = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SendToSupplier"]);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray); //Email ids                           
                MailAddressCollection adrTo = null;
                MailAddressCollection adrCC = new MailAddressCollection();
                System.Net.Mail.MailAddress adrFrom = new MailAddress(SessionManager.LoginEmail);
                System.Net.Mail.MailAddress ReplyTo = new MailAddress(EmailID);
                System.Net.Mail.MailAddress ReplyCC = new MailAddress(SessionManager.LoginEmail);
                adrCC.Add(ReplyCC);

                string Suppliers = "";
                Suppliers = strsupplierNamesArray.Replace("||", ",");
                Array aryToCompanyIds = Functions.JavascriptStringToArray(strToCompanyIdsArray);//Company Id's
                Array arySupplierNames = Functions.JavascriptStringToArray(strsupplierNamesArray);//Supplier Name

                bool isSentSuccessfully = false;
                string sectionName = reportNo == (int)Report.List.RequirementWithBOM ? "BOM" : "PurchaseHub";
                string strTitle = reportNo == (int)Report.List.RequirementWithBOM ? "HUBRFQ" : "Price Request";
                BLL.CSVExportLog.InsertMailLog(Suppliers, Id, sectionName);
                Int32 srNo = 0;
                for (int i = 0; i < aryToCompanyIds.Length; i++)
                {
                    srNo = i + 1;
                    string csvFile = string.Empty;
                    adrTo = new MailAddressCollection();
                    if (((string[])(aryToLogins))[i] != "")
                    {
                        csvFile = FileUploadManager.ExportToCSVForClientList(reportNo, Id, CurrencyCode, Convert.ToInt32(aryToCompanyIds.GetValue(i)), srNo);
                        BLL.CSVExportLog.Insert(
                                     Convert.ToInt32(aryToCompanyIds.GetValue(i))
                                     , csvFile
                                     , SessionManager.LoginID
                                     , sectionName
                                     , aryToLogins.GetValue(i).ToString()
                                     , Id
                                     , strSubject
                                     , strMessage
                                 );


                        adrTo.Add(aryToLogins.GetValue(i).ToString());
                        string bodyMessage = OutgoingMailManager.BodyMailMessage(strMessage, arySupplierNames.GetValue(i).ToString());

                        isSentSuccessfully = OutgoingMailManager.SendAttachedDocumentToUsers(strSubject, adrTo, adrFrom, adrCC, null, ReplyTo, bodyMessage, csvFile, null);
                        adrTo = null;
                    }
                }
            }

            catch { }
        }

        public void ReleaseHUBRFQSendMailTemplate(int? BomID, bool isAllReleased, int? RequirementNumber, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                string strisblank = "-";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedHUBRFQMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                BOM bom = BOM.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "HUBRFQIsReleased"));
                HTMlString = HTMlString.Replace("#Code#", bom.BOMCode + "( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", bom.BOMName);
                HTMlString = HTMlString.Replace("#Company#", bom.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", bom.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", bom.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(bom.QuoteRequired));

                List<CustomerRequirement> lst = null;

                lst = CustomerRequirement.GetBOMListForCustomerRequirement(BomID, SessionManager.ClientID);
                foreach (CustomerRequirement cReq in lst)
                {
                    if (cReq.HasHubSourcingResult.Value)
                    {
                        if ((isAllReleased && cReq.POHubReleaseBy > 0) || RequirementNumber == cReq.CustomerRequirementId)
                        {
                            string Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                            Line = Line + "</tr>";
                            strMail.Append(Line);
                        }
                    }

                }

                string strSourcingtable = string.Empty;
                string strQuotes = string.Empty;
                List<SourcingResult> lstSr = null;
                if (isAllReleased)
                {
                    string Line = "";
                    lstSr = SourcingResult.GetListForBOMSourcingResult(BomID, Convert.ToBoolean(SessionManager.IsPOHub));
                    foreach (SourcingResult sor in lstSr)
                    {
                        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                        if (sor.ReReleased == 1)
                        {
                            SourcingResult sorlog = SourcingResult.GetSourcingLog(sor.SourcingResultId);

                            if (sorlog != null)
                            {
                                strQuotes = "";
                                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                                {
                                    strQuotes += qt.QuoteNumber;
                                }

                                string[] splitPipeValue = sorlog.ChangeNotes.Split(',');
                                Line = "<tr>";
                                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                                if (splitPipeValue.Contains("PartNo") || splitPipeValue.Contains("Notes"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td  class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                                }
                                if (splitPipeValue.Contains("Manufacturer"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                                }
                                if (splitPipeValue.Contains("Product") || splitPipeValue.Contains("Package"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                                }
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                                if (splitPipeValue.Contains("Quantity") || splitPipeValue.Contains("DeliveryDate"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                                }
                                if (splitPipeValue.Contains("UpliftPrice"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                                }
                                if (splitPipeValue.Contains("Region"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                                }
                                if (splitPipeValue.Contains("EstimatedShippingCost"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                                }

                                Line = Line + "</tr>";
                                strSourcing.Append(Line);
                                strSourcingtable = "";
                                sorlog = null;
                            }
                            else
                            {
                                strQuotes = "";
                                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                                {
                                    strQuotes += qt.QuoteNumber;
                                }
                                Line = "<tr>";
                                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                                Line = Line + "</tr>";
                                strSourcing.Append(Line);
                                strSourcingtable = "";
                            }
                        }
                        else
                        {
                            strQuotes = "";
                            foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                            {
                                strQuotes += qt.QuoteNumber;
                            }
                            Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                            Line = Line + "</tr>";
                            strSourcing.Append(Line);
                            strSourcingtable = "";
                        }


                    }

                }
                else
                {
                    string Line = "";
                    lstSr = SourcingResult.GetListForBOMCustomerRequirement(RequirementNumber, Convert.ToBoolean(SessionManager.IsPOHub));
                    foreach (SourcingResult sor in lstSr)
                    {
                        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                        if (sor.ReReleased == 1)
                        {
                            SourcingResult sorlog = SourcingResult.GetSourcingLog(sor.SourcingResultId);
                            if (sorlog != null)
                            {
                                strQuotes = "";
                                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                                {
                                    strQuotes += qt.QuoteNumber;
                                }

                                string[] splitPipeValue = sorlog.ChangeNotes.Split(',');
                                Line = "<tr>";
                                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                                if (splitPipeValue.Contains("PartNo") || splitPipeValue.Contains("Notes"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td  class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                                }
                                if (splitPipeValue.Contains("Manufacturer"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                                }
                                if (splitPipeValue.Contains("Product") || splitPipeValue.Contains("Package"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                                }
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                                if (splitPipeValue.Contains("Quantity") || splitPipeValue.Contains("DeliveryDate"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                                }
                                if (splitPipeValue.Contains("UpliftPrice"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                                }
                                if (splitPipeValue.Contains("Region"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                                }
                                if (splitPipeValue.Contains("EstimatedShippingCost"))
                                {
                                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                                }
                                else
                                {
                                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                                }

                                Line = Line + "</tr>";
                                strSourcing.Append(Line);
                                strSourcingtable = "";
                            }
                            else
                            {
                                //related quotes
                                strQuotes = "";
                                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                                {
                                    strQuotes += qt.QuoteNumber;
                                }
                                Line = "<tr>";
                                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                                Line = Line + "</tr>";
                                strSourcing.Append(Line);
                                strSourcingtable = "";
                            }
                        }
                        else
                        {
                            //related quotes
                            strQuotes = "";
                            foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                            {
                                strQuotes += qt.QuoteNumber;
                            }
                            Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                            Line = Line + "</tr>";
                            strSourcing.Append(Line);
                            strSourcingtable = "";
                        }


                    }
                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;

            }
            catch (Exception)
            {
                bodyHTML = "";
            }

        }
        public void ReleaseHUBRFQSendMailTemplateBOMManager(int? BomID, bool isAllReleased, string RequirementNumbers, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                //string strisblank = "-";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                string ReleaseStatus = "";
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID); // added by rahil use existing bommanager
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);
                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);

                if (lst.Count(x => x.ReqStatus < 4 && x.IsNoBid != true) > 0)
                {
                    ReleaseStatus = "BOMManagerPartialRelease";
                }
                else
                {
                    ReleaseStatus = "BOMManagerRelease";
                }

                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", ReleaseStatus));// added by rahil need to check the value
                HTMlString = HTMlString.Replace("#Code#", "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_BOMManagerDetail.aspx?BOM=" + BomID + "'>" + BOMList.BOMManagerCode + "</a>");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));

                RequirementNumbers = RequirementNumbers.Substring(0, RequirementNumbers.Length - 1);
                List<int> CustomerRequirementIDs = RequirementNumbers.Split('|').Select(int.Parse).ToList();

                foreach (BOMManagerContract cReq in lst)
                {
                    if (cReq.ReqStatus != null)
                    {
                        if (CustomerRequirementIDs.Any(x => x == cReq.CustomerRequirementId))
                        {
                            string Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMManagerCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                            Line = Line + "</tr>";
                            strMail.Append(Line);
                        }
                    }
                }
                #region commented code as it is not required now

                //string strSourcingtable = string.Empty;
                //string strQuotes = string.Empty;
                //List<SourcingResult> lstSr = null;
                //List<SourcingResult> lstSrlog = null;
                //if (isAllReleased == true)
                //{
                //    string Line = "";
                //    lstSr = SourcingResult.GetListForBOMManagerSourcingResult(BomID, Convert.ToBoolean(SessionManager.IsPOHub)); // added by rahil need to implement
                //    // lstSrlog = SourcingResult.GetListForBOMCustomerRequirementLogDetail(RequirementNumber, Convert.ToBoolean(SessionManager.IsPOHub));
                //    foreach (SourcingResult sor in lstSr)
                //    {
                //        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                //        // bool IsResultIdFound = false;
                //        if (sor.ReReleased == 1)
                //        {
                //            SourcingResult sorlog = SourcingResult.GetSourcingLog(sor.SourcingResultId);// Added  by rahil need to implement
                //            // foreach (SourcingResult sorlog in lstSrlog)
                //            // {
                //            if (sorlog != null)
                //            {
                //                //related quotes
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }

                //                //PartNo,Notes,DateCode,Manufacturer,Product,Package,Quantity,DeliveryDate,BuyPrice1,UpliftPrice,Region,EstimatedShippingCost
                //                //IsResultIdFound = true;
                //                string[] splitPipeValue = sorlog.ChangeNotes.Split(',');
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                if (splitPipeValue.Contains("PartNo") || splitPipeValue.Contains("Notes"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td  class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Manufacturer"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Product") || splitPipeValue.Contains("Package"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                if (splitPipeValue.Contains("Quantity") || splitPipeValue.Contains("DeliveryDate"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }

                //                if (splitPipeValue.Contains("UpliftPrice"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Region"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                if (splitPipeValue.Contains("EstimatedShippingCost"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }

                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //                sorlog = null;
                //            }
                //            else
                //            {
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                // Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //            }
                //        }
                //        else
                //        {
                //            strQuotes = "";
                //            foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //            {
                //                strQuotes += qt.QuoteNumber;
                //            }
                //            Line = "<tr>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //            // Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //            Line = Line + "</tr>";
                //            strSourcing.Append(Line);
                //            strSourcingtable = "";
                //        }


                //    }

                //}
                //else
                //{
                //    string Line = "";
                //    lstSr = SourcingResult.GetListForBOMCustomerRequirement(RequirementNumber, Convert.ToBoolean(SessionManager.IsPOHub));// added by rahil need to implementd
                //    // lstSrlog= SourcingResult.GetListForBOMCustomerRequirementLogDetail(RequirementNumber, Convert.ToBoolean(false));
                //    foreach (SourcingResult sor in lstSr)
                //    {
                //        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                //        // bool IsResultIdFound = false;
                //        if (sor.ReReleased == 1)
                //        {
                //            //foreach (SourcingResult sorlog in lstSrlog)
                //            //{
                //            SourcingResult sorlog = SourcingResult.GetSourcingLog(sor.SourcingResultId); // added by rahil use existing above
                //            if (sorlog != null)
                //            {
                //                //related quotes
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId)) // added by rahil use existing above
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }
                //                //PartNo,Notes,DateCode,Manufacturer,Product,Package,Quantity,DeliveryDate,BuyPrice1,UpliftPrice,Region,EstimatedShippingCost
                //                // IsResultIdFound = true;
                //                string[] splitPipeValue = sorlog.ChangeNotes.Split(',');
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                if (splitPipeValue.Contains("PartNo") || splitPipeValue.Contains("Notes"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td  class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Manufacturer"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Product") || splitPipeValue.Contains("Package"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                if (splitPipeValue.Contains("Quantity") || splitPipeValue.Contains("DeliveryDate"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }
                //                //if (splitPipeValue.Contains("BuyPrice1") || splitPipeValue.Contains("UpliftPrice"))
                //                //{
                //                //    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                //}
                //                //else
                //                //{
                //                //    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                //}
                //                if (splitPipeValue.Contains("UpliftPrice"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Region"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                if (splitPipeValue.Contains("EstimatedShippingCost"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }

                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //            }
                //            else
                //            {
                //                //related quotes
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                //Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //            }
                //        }
                //        else
                //        {
                //            //related quotes
                //            strQuotes = "";
                //            foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //            {
                //                strQuotes += qt.QuoteNumber;
                //            }
                //            Line = "<tr>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //            //Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //            Line = Line + "</tr>";
                //            strSourcing.Append(Line);
                //            strSourcingtable = "";
                //        }


                //    }
                //}
                #endregion commented code as it is not required now
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;

            }
            catch (Exception)
            {
                bodyHTML = "";
            }

        }

        public void CloseHUBRFQSendMailTemplateBOMManager(int? BomID, bool isAllReleased, int? RequirementNumber, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                //string strisblank = "-";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/CloseBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID); // added by rahil use existing bommanager
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "BOMManager"));// added by rahil need to check the value
                HTMlString = HTMlString.Replace("#Code#", BOMList.BOMManagerCode + "( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));

                //List<CustomerRequirement> lst = null;
                // if (isAllReleased == true)
                // {
                //lst = CustomerRequirement.GetBOMListForCustomerRequirement(BomID, SessionManager.ClientID); // added by rahil use existing bommanager
                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);
                //}
                //else
                //{
                //    lst = CustomerRequirement.GetListForCustomerRequirement(RequirementNumber, SessionManager.ClientID);
                //}
                //List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQForMail(BomID, SessionManager.ClientID);
                foreach (BOMManagerContract cReq in lst)
                {
                    if (cReq.HasHubSourcingResult.Value)
                    {
                        if (isAllReleased == true && cReq.POHubReleaseBy > 0)
                        {
                            string Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMManagerCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                            Line = Line + "</tr>";
                            strMail.Append(Line);
                        }
                        else if (RequirementNumber == cReq.CustomerRequirementId)
                        {
                            string Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMManagerCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                            //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                            Line = Line + "</tr>";
                            strMail.Append(Line);
                        }
                    }

                }
                #region commented code as it is not required now

                //string strSourcingtable = string.Empty;
                //string strQuotes = string.Empty;
                //List<SourcingResult> lstSr = null;
                //List<SourcingResult> lstSrlog = null;
                //if (isAllReleased == true)
                //{
                //    string Line = "";
                //    lstSr = SourcingResult.GetListForBOMManagerSourcingResult(BomID, Convert.ToBoolean(SessionManager.IsPOHub)); // added by rahil need to implement
                //    // lstSrlog = SourcingResult.GetListForBOMCustomerRequirementLogDetail(RequirementNumber, Convert.ToBoolean(SessionManager.IsPOHub));
                //    foreach (SourcingResult sor in lstSr)
                //    {
                //        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                //        // bool IsResultIdFound = false;
                //        if (sor.ReReleased == 1)
                //        {
                //            SourcingResult sorlog = SourcingResult.GetSourcingLog(sor.SourcingResultId);// Added  by rahil need to implement
                //            // foreach (SourcingResult sorlog in lstSrlog)
                //            // {
                //            if (sorlog != null)
                //            {
                //                //related quotes
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }

                //                //PartNo,Notes,DateCode,Manufacturer,Product,Package,Quantity,DeliveryDate,BuyPrice1,UpliftPrice,Region,EstimatedShippingCost
                //                //IsResultIdFound = true;
                //                string[] splitPipeValue = sorlog.ChangeNotes.Split(',');
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                if (splitPipeValue.Contains("PartNo") || splitPipeValue.Contains("Notes"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td  class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Manufacturer"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Product") || splitPipeValue.Contains("Package"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                if (splitPipeValue.Contains("Quantity") || splitPipeValue.Contains("DeliveryDate"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }

                //                if (splitPipeValue.Contains("UpliftPrice"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Region"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                if (splitPipeValue.Contains("EstimatedShippingCost"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }

                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //                sorlog = null;
                //            }
                //            else
                //            {
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                // Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //            }
                //        }
                //        else
                //        {
                //            strQuotes = "";
                //            foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //            {
                //                strQuotes += qt.QuoteNumber;
                //            }
                //            Line = "<tr>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //            // Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //            Line = Line + "</tr>";
                //            strSourcing.Append(Line);
                //            strSourcingtable = "";
                //        }


                //    }

                //}
                //else
                //{
                //    string Line = "";
                //    lstSr = SourcingResult.GetListForBOMCustomerRequirement(RequirementNumber, Convert.ToBoolean(SessionManager.IsPOHub));// added by rahil need to implementd
                //    // lstSrlog= SourcingResult.GetListForBOMCustomerRequirementLogDetail(RequirementNumber, Convert.ToBoolean(false));
                //    foreach (SourcingResult sor in lstSr)
                //    {
                //        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                //        // bool IsResultIdFound = false;
                //        if (sor.ReReleased == 1)
                //        {
                //            //foreach (SourcingResult sorlog in lstSrlog)
                //            //{
                //            SourcingResult sorlog = SourcingResult.GetSourcingLog(sor.SourcingResultId); // added by rahil use existing above
                //            if (sorlog != null)
                //            {
                //                //related quotes
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId)) // added by rahil use existing above
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }
                //                //PartNo,Notes,DateCode,Manufacturer,Product,Package,Quantity,DeliveryDate,BuyPrice1,UpliftPrice,Region,EstimatedShippingCost
                //                // IsResultIdFound = true;
                //                string[] splitPipeValue = sorlog.ChangeNotes.Split(',');
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                if (splitPipeValue.Contains("PartNo") || splitPipeValue.Contains("Notes"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td  class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Manufacturer"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Product") || splitPipeValue.Contains("Package"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                }
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                if (splitPipeValue.Contains("Quantity") || splitPipeValue.Contains("DeliveryDate"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                }
                //                //if (splitPipeValue.Contains("BuyPrice1") || splitPipeValue.Contains("UpliftPrice"))
                //                //{
                //                //    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                //}
                //                //else
                //                //{
                //                //    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                //}
                //                if (splitPipeValue.Contains("UpliftPrice"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                }
                //                if (splitPipeValue.Contains("Region"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                }
                //                if (splitPipeValue.Contains("EstimatedShippingCost"))
                //                {
                //                    Line = Line + "<td Style='background-color:yellow' class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }
                //                else
                //                {
                //                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                }

                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //            }
                //            else
                //            {
                //                //related quotes
                //                strQuotes = "";
                //                foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //                {
                //                    strQuotes += qt.QuoteNumber;
                //                }
                //                Line = "<tr>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //                //Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //                Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //                Line = Line + "</tr>";
                //                strSourcing.Append(Line);
                //                strSourcingtable = "";
                //            }
                //        }
                //        else
                //        {
                //            //related quotes
                //            strQuotes = "";
                //            foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                //            {
                //                strQuotes += qt.QuoteNumber;
                //            }
                //            Line = "<tr>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                //            //Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                //            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                //            Line = Line + "</tr>";
                //            strSourcing.Append(Line);
                //            strSourcingtable = "";
                //        }


                //    }
                //}
                #endregion commented code as it is not required now
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;

            }
            catch (Exception)
            {
                bodyHTML = "";
            }

        }

        public void NoBidSendMailTemplateBOMManager(int? BomID, bool isAllNoBid, string RequirementNumbers, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                // string strisblank = "-";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID); // added by rahil use existing bommanager
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "BOMManagerNoBid"));// added by rahil need to check the value
                HTMlString = HTMlString.Replace("#Code#", "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_BOMManagerDetail.aspx?BOM=" + BomID + "'>" + BOMList.BOMManagerCode + "</a>");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));

                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);
                RequirementNumbers = RequirementNumbers.Substring(0, RequirementNumbers.Length - 1);
                List<int> CustomerRequirementIDs = RequirementNumbers.Split('|').Select(int.Parse).ToList();

                foreach (BOMManagerContract cReq in lst)
                {
                    if (CustomerRequirementIDs.Any(x => x == cReq.CustomerRequirementId))
                    {
                        string Line = "<tr>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.Part + "<br />" + cReq.CustomerPart + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.CurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                        Line = Line + "</tr>";
                        strMail.Append(Line);
                    }
                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;

            }
            catch (Exception)
            {
                bodyHTML = "";
            }

        }

        public void RecallSendMailTemplateBOMManager(int? BomID, bool isAllNoBid, string RequirementNumbers, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                //string strisblank = "-";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID); // added by rahil use existing bommanager
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "BOMManagerRecall"));// added by rahil need to check the value
                HTMlString = HTMlString.Replace("#Code#", "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_BOMManagerDetail.aspx?BOM=" + BomID + "'>" + BOMList.BOMManagerCode + "</a>");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));

                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);
                RequirementNumbers = RequirementNumbers.Substring(0, RequirementNumbers.Length - 1);
                List<int> CustomerRequirementIDs = RequirementNumbers.Split('|').Select(int.Parse).ToList();

                foreach (BOMManagerContract cReq in lst)
                {

                    if (CustomerRequirementIDs.Any(x => x == cReq.CustomerRequirementId))
                    {
                        string Line = "<tr>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.Part + "<br />" + cReq.CustomerPart + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.CurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                        Line = Line + "</tr>";
                        strMail.Append(Line);
                    }

                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;

            }
            catch (Exception)
            {
                bodyHTML = "";
            }

        }

        public void RecallNoBidSendMailTemplateBOMManager(int? BomID, bool isAllNoBid, string RequirementNumbers, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                //string strisblank = "-";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID); // added by rahil use existing bommanager
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "BOMManagerRecallNoBid"));// added by rahil need to check the value
                HTMlString = HTMlString.Replace("#Code#", "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_BOMManagerDetail.aspx?BOM=" + BomID + "'>" + BOMList.BOMManagerCode + "</a>");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));

                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);
                RequirementNumbers = RequirementNumbers.Substring(0, RequirementNumbers.Length - 1);
                List<int> CustomerRequirementIDs = RequirementNumbers.Split('|').Select(int.Parse).ToList();

                foreach (BOMManagerContract cReq in lst)
                {
                    if (CustomerRequirementIDs.Any(x => x == cReq.CustomerRequirementId))
                    {
                        string Line = "<tr>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.Part + "<br />" + cReq.CustomerPart + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                        Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.CurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                        //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                        Line = Line + "</tr>";
                        strMail.Append(Line);
                    }
                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;

            }
            catch (Exception)
            {
                bodyHTML = "";
            }

        }
        [WebMethod()]
        public void NotifyBomManagerCloseToSaleperson(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllReleased, int? RequirementID)
        {
            try
            {
                string bodyHTML = "";


                CloseHUBRFQSendMailTemplateBOMManager(bomId, isAllReleased, RequirementID, out bodyHTML);
                //strSubject = strSubject + " ( "+bomCode+" )";// Send Bom Code in Subject
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyCloseBomManager(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        public void ApprovalMailTemplate(int? BomID, bool isAllReleased, int? RequirementNo, int? sourcingResultNo, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                StringBuilder strSourcing = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ApproveHUBRFQMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                BOM bom = BOM.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "HUBRFQ"));
                HTMlString = HTMlString.Replace("#Code#", bom.BOMCode + "( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", bom.BOMName);
                HTMlString = HTMlString.Replace("#Company#", bom.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", bom.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", bom.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(bom.QuoteRequired));

                CustomerRequirement cReq = CustomerRequirement.Get(RequirementNo);
                if (cReq != null)
                {
                    string Line = "<tr>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                    //Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                    //Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                    //Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                    Line = Line + "</tr>";
                    strMail.Append(Line);
                }
                SourcingResult sor = SourcingResult.Get(sourcingResultNo);
                if (sor != null)
                {
                    string Line = "<tr>";
                    Line = Line + "<td class=\"Textclass\">" + sor.POHubSupplierName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + sor.OfferStatusChangeEmployeeName + "<br />" + Functions.FormatDate(sor.OfferStatusChangeDate) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.OriginalPrice, sor.ActualCurrencyCode) + "<br />" + Functions.FormatCurrency(sor.ActualPrice, SessionManager.ClientCurrencyCode) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + sor.SourcingTable == "PQ" ? "YES" : "-" + "<br />" + sor.RegionName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                    Line = Line + "</tr>";
                    strSourcing.Append(Line);
                }


                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                HTMlString = HTMlString.Replace("#approvedlink#", HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority + "/" + string.Format("Ord_BOMDetail.aspx ? BOM ={0}&ActionType={1}&sor={2} ", BomID, "Approval", sourcingResultNo));
                HTMlString = HTMlString.Replace("#reject#", HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority + "/" + string.Format("Ord_BOMDetail.aspx ? BOM ={0}&ActionType={1}&sor={2} ", BomID, "Reject", sourcingResultNo));
                HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                strSourcing = null;
                bodyHTML = HTMlString;


            }
            catch (Exception)
            {
                bodyHTML = "";
            }



        }

        public void GenerateAndSendHUBRFQEmail(int? BomID, bool isAll, int? RequirementNumber, bool isNoBid, out string bodyHTML)
        {
            try
            {
                bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedHUBRFQMail.htm");

                using (StreamReader sr = File.OpenText(FilePath))
                {
                    HTMlString = sr.ReadToEnd();
                }

                BOM bom = BOM.Get(BomID);
                HTMlString = HTMlString.Replace("#Code#", bom.BOMCode + " ( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", bom.BOMName);
                HTMlString = HTMlString.Replace("#Company#", bom.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", bom.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", bom.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(bom.QuoteRequired));

                List<CustomerRequirement> lst = CustomerRequirement.GetBOMListForCustomerRequirement(BomID, SessionManager.ClientID);

                if (isNoBid)
                {
                    // This is the logic from NoBidHUBRFQSendMailTemplate
                    HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "HUBRFQIsNoBid"));

                    foreach (CustomerRequirement cReq in lst)
                    {
                        if (cReq.IsNoBid.HasValue && cReq.IsNoBid.Value)
                        {
                            if (isAll || RequirementNumber == cReq.CustomerRequirementId)
                            {
                                string Line = "<tr>";
                                Line += "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                                Line += "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                                Line += "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                                Line += "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                                Line += "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                                Line += "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                                Line += "</tr>";
                                strMail.Append(Line);
                            }
                        }
                    }
                    StrmailData = Convert.ToString(strMail);
                    HTMlString = HTMlString.Replace("#trline#", StrmailData);
                    HTMlString = HTMlString.Replace("#trSrline#", ""); // No sourcing results for NoBid
                }
                else // This is the logic from ReleaseHUBRFQSendMailTemplate
                {
                    HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "HUBRFQIsReleased"));
                    string strisblank = "-";
                    StringBuilder strSourcing = new StringBuilder();

                    foreach (CustomerRequirement cReq in lst)
                    {
                        if (cReq.HasHubSourcingResult.HasValue && cReq.HasHubSourcingResult.Value)
                        {
                            if ((isAll && cReq.POHubReleaseBy > 0) || RequirementNumber == cReq.CustomerRequirementId)
                            {
                                string Line = "<tr>";
                                Line += "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                                Line += "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                                Line += "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                                Line += "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                                Line += "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                                Line += "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                                Line += "</tr>";
                                strMail.Append(Line);
                            }
                        }
                    }

                    string strSourcingtable = string.Empty;
                    string strQuotes = string.Empty;
                    List<SourcingResult> lstSr = null;
                    if (isAll)
                    {
                        lstSr = SourcingResult.GetListForBOMSourcingResult(BomID, Convert.ToBoolean(SessionManager.IsPOHub));
                    }
                    else
                    {
                        lstSr = SourcingResult.GetListForBOMCustomerRequirement(RequirementNumber, Convert.ToBoolean(SessionManager.IsPOHub));
                    }

                    foreach (SourcingResult sor in lstSr)
                    {
                        strSourcingtable = sor.SourcingTable == "PQ" ? "YES" : strisblank;
                        strQuotes = "";
                        foreach (BLL.Quote qt in Quote.GetListForSourcingResult(sor.SourcingResultId))
                        {
                            strQuotes += qt.QuoteNumber;
                        }

                        string Line = "<tr>";
                        Line += "<td class=\"Textclass\">" + sor.SupplierType + "<br/>" + strQuotes + "</td>";
                        Line += "<td class=\"Textclass\">" + sor.Part + "<br />" + Functions.ReplaceLineBreaks(sor.Notes) + "</td>";
                        Line += "<td class=\"Textclass\">" + sor.ManufacturerName + "<br />" + sor.SupplierDateCode + "</td>";
                        Line += "<td class=\"Textclass\">" + sor.ProductName + "<br />" + sor.PackageName + "</td>";
                        Line += "<td class=\"Textclass\">" + Functions.FormatDate(sor.OfferStatusChangeDate) + "<br />" + sor.OfferStatusChangeEmployeeName + "</td>";
                        Line += "<td class=\"Textclass\">" + sor.Quantity + "<br />" + Functions.FormatDate(sor.DeliveryDate) + "</td>";
                        Line += "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.Price, sor.CurrencyCode) + "<br />" + Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(sor.Price, sor.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode) + "</td>";
                        Line += "<td class=\"Textclass\">" + strSourcingtable + "<br />" + sor.RegionName + "</td>";
                        Line += "<td class=\"Textclass\">" + Functions.FormatCurrency(sor.EstimatedShippingCost, sor.CurrencyCode) + "</td>";
                        Line += "</tr>";
                        strSourcing.Append(Line);
                        strSourcingtable = "";
                    }

                    StrmailData = Convert.ToString(strMail);
                    HTMlString = HTMlString.Replace("#trline#", StrmailData);
                    HTMlString = HTMlString.Replace("#trSrline#", Convert.ToString(strSourcing));
                    strSourcing = null;
                }

                bodyHTML = HTMlString;
            }
            catch (Exception)
            {
                bodyHTML = "";
            }
        }

        public void NoBidHUBRFQSendMailTemplate(int? BomID, bool isAllNoBid, int? RequirementNumber, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/ReleasedHUBRFQMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                BOM bom = BOM.Get(BomID);
                HTMlString = HTMlString.Replace("#HUBRFQSTATUS#", Functions.GetGlobalResource("FormFields", "HUBRFQIsNoBid"));
                HTMlString = HTMlString.Replace("#Code#", bom.BOMCode + "( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", bom.BOMName);
                HTMlString = HTMlString.Replace("#Company#", bom.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", bom.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", bom.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(bom.QuoteRequired));
                List<CustomerRequirement> lst = CustomerRequirement.GetBOMListForCustomerRequirement(BomID, SessionManager.ClientID);
                foreach (CustomerRequirement cReq in lst)
                {
                    if (cReq.IsNoBid.Value)
                    {
                        if (isAllNoBid || RequirementNumber == cReq.CustomerRequirementId)
                        {
                            string Line = "<tr>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.Part + "<br />" + cReq.CustomerPart + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                            Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                            Line = Line + "</tr>";
                            strMail.Append(Line);
                        }
                    }
                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                bodyHTML = HTMlString;


            }
            catch (Exception)
            {
                bodyHTML = "";
            }



        }

        [WebMethod()]
        public void NotifyNoBidBom(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllNoBid, int? RequirementID)
        {
            try
            {
                string bodyHTML = "";


                NoBidHUBRFQSendMailTemplate(bomId, isAllNoBid, RequirementID, out bodyHTML);
                //strSubject = strSubject + " ( "+bomCode+" )";// Send Bom Code in Subject
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBom(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //lstToLoginID.Add(8348);

                BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , 8349
                    , strSubject
                    , bodyHTML
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }


        [WebMethod()]
        public void NotifyReleaseBom(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllReleased, int? RequirementID)
        {
            try
            {
                string bodyHTML = "";


                ReleaseHUBRFQSendMailTemplate(bomId, isAllReleased, RequirementID, out bodyHTML);
                //strSubject = strSubject + " ( "+bomCode+" )";// Send Bom Code in Subject
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBom(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                lstToLoginID.Add(8349);

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , bodyHTML
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }
        [WebMethod()]
        public void NotifyApproval(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllReleased, int? RequirementID, int? intSourcingResultNo)
        {
            try
            {
                string bodyHTML = "";


                ApprovalMailTemplate(bomId, isAllReleased, RequirementID, intSourcingResultNo, out bodyHTML);
                //strSubject = strSubject + " ( "+bomCode+" )";// Send Bom Code in Subject
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBom(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }
        // [002] code start Here
        public void SendPoHUBMailGroup(string strSubject, int? BomID, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/PurchaseRequestMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                BOM bom = BOM.Get(BomID);
                string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                var hubrfqLink = url + string.Format("Ord_BOMDetail.aspx?BOM={0}", bom.BOMId);

                HTMlString = HTMlString.Replace("#Code#", bom.BOMCode + "( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", "<a href=\"" + hubrfqLink + "\">" + bom.BOMName + "</a>");
                HTMlString = HTMlString.Replace("#Company#", bom.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", bom.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", bom.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(bom.QuoteRequired));
                HTMlString = HTMlString.Replace("#NOTES#", bom.Notes);

                List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQForMail(BomID, SessionManager.ClientID);
                foreach (CustomerRequirement cReq in lst)
                {
                    string Line = "<tr>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (cReq.AlternativesAccepted == false ? "NO" : "YES") + "<br />" + (cReq.RepeatBusiness == false ? "NO" : "YES") + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.LyticaManufacturerRef + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.LyticaAveragePrice + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.LyticaTargetPrice + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.LyticaMarketLeading + "</td>";
                    Line = Line + "</tr>";
                    strMail.Append(Line);


                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                bodyHTML = HTMlString;
                List<BLL.MailGroupMember> lstMail = null;
                lstMail = BLL.MailGroupMember.GetEmailListByGroup("HUBRFQ Group");
                MailAddressCollection adrTo = null;
                adrTo = new MailAddressCollection();
                foreach (BLL.MailGroupMember mm in lstMail)
                {
                    if (!string.IsNullOrEmpty(mm.EmailID))
                    {
                        adrTo.Add(mm.EmailID);
                        // adrTo.Add("<EMAIL>");
                    }
                }
                string strEmailFrom = SessionManager.LoginEmail;
                // string strEmailFrom = "<EMAIL>";


                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(HTMlString), adrTo, adrFrom, null, null, adrReplyTo, true);
                OutgoingMailManager.SendUserMailDocument(strSubject, adrTo, adrFrom, null, null, null, HTMlString, "", "");
            }
            catch (Exception)
            {
                bodyHTML = "";
            }
        }

        [WebMethod()]
        public void SendCustomerApiUserMail(string strUsername, string strPassword, string toEmail, string strSubject)
        {// send mail in group
            try
            {
                //bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";


                string Line = "";
                Line = Line + "<html><body>";
                Line = Line + "Hi, <br/><br/> Please find below credentials. <br/>";
                Line = Line + "UserId : " + strUsername + "<br/>";
                //Line = Line + "Password : " + strPassword + "<br/><br/>";
                Line = Line + "Regards<br/> Rebound";
                Line = Line + "</body></Html>";
                strMail.Append(Line);


                //}
                StrmailData = Convert.ToString(strMail);
                HTMlString = StrmailData;
                //bodyHTML = HTMlString;
                //List<BLL.MailGroupMember> lstMail = null;
                //lstMail = BLL.MailGroupMember.GetEmailListByGroup("HUBRFQ Group");
                MailAddressCollection adrTo = new MailAddressCollection();
                adrTo.Add(toEmail);
                string strEmailFrom = SessionManager.LoginEmail;
                // string strEmailFrom = "<EMAIL>";


                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(HTMlString), adrTo, adrFrom, null, null, adrReplyTo, true);
                OutgoingMailManager.SendUserMailDocument(strSubject, adrTo, adrFrom, null, null, null, HTMlString, "", "");


            }
            catch (Exception ex)
            {
                throw ex;
            }



        }
        [WebMethod()]
        public void NotifyReleaseBomManager(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllReleased, string RequirementIDs)
        {
            try
            {
                string bodyHTML = "";


                ReleaseHUBRFQSendMailTemplateBOMManager(bomId, isAllReleased, RequirementIDs, out bodyHTML);
                //strSubject = strSubject + " ( "+bomCode+" )";// Send Bom Code in Subject
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBomManager(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                string GTNotificationHTML = bodyHTML;
                GTNotificationHTML = Functions.CleanCarriageReturnTabAndNewLineCharacter(GTNotificationHTML);
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , GTNotificationHTML
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }
        [WebMethod()]
        public void NotifyNoBidBomManager(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllNoBid, string RequirementIDs)
        {
            try
            {
                string bodyHTML = "";


                NoBidSendMailTemplateBOMManager(bomId, isAllNoBid, RequirementIDs, out bodyHTML);
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBomManager(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                string GTNotificationHTML = bodyHTML;
                GTNotificationHTML = Functions.CleanCarriageReturnTabAndNewLineCharacter(GTNotificationHTML);
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , GTNotificationHTML
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }
        [WebMethod()]
        public void NotifyRecallBomManager(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllNoBid, string RequirementIDs)
        {
            try
            {
                string bodyHTML = "";


                RecallSendMailTemplateBOMManager(bomId, isAllNoBid, RequirementIDs, out bodyHTML);
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBomManager(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                string GTNotificationHTML = bodyHTML;
                GTNotificationHTML = Functions.CleanCarriageReturnTabAndNewLineCharacter(GTNotificationHTML);
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , GTNotificationHTML
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }
        [WebMethod()]
        public void NotifyRecallNoBidBomManager(string strToLoginsArray, string strToGroupsArray, string strSubject, int bomId, string bomCode, string bomName, int? bomCompanyNo, string bomCompanyName, bool isAllNoBid, string RequirementIDs)
        {
            try
            {
                string bodyHTML = "";


                RecallNoBidSendMailTemplateBOMManager(bomId, isAllNoBid, RequirementIDs, out bodyHTML);
                strSubject = strSubject + " ( " + bomName + " )";// new code added by Prakash. Now Send Bom Name in Subject.
                string strMessage = MailTemplateManager.GetMessage_NotifyReleaseBomManager(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(aryToLogins.GetValue(i))))
                    {
                        int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }
                string GTNotificationHTML = bodyHTML;
                GTNotificationHTML = Functions.CleanCarriageReturnTabAndNewLineCharacter(GTNotificationHTML);
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , GTNotificationHTML
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        // [002] code End Here
        public void SendPoHUBMailGroupBOMManager(string strSubject, int? BomID, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/PurchaseRequestBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID);
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);

                HTMlString = HTMlString.Replace("#Code#", "<a href='" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_BOMManagerSourcing.aspx?BOM=" + BomID + "'>" + BOMList.BOMManagerCode + "</a>");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));
                HTMlString = HTMlString.Replace("#NOTES#", BOMList.Notes);

                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);
                //List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQForMail(BomID, SessionManager.ClientID);
                foreach (BOMManagerContract cReq in lst)
                {
                    string Line = "<tr>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMManagerCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (cReq.AlternativesAccepted == false ? "NO" : "YES") + "<br />" + (cReq.RepeatBusiness == false ? "NO" : "YES") + "</td>";
                    Line = Line + "</tr>";
                    strMail.Append(Line);


                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                bodyHTML = HTMlString;
                List<BLL.MailGroupMember> lstMail = null;
                lstMail = BLL.MailGroupMember.GetEmailListByGroup("HUBRFQ Group");
                MailAddressCollection adrTo = null;
                adrTo = new MailAddressCollection();
                foreach (BLL.MailGroupMember mm in lstMail)
                {
                    if (!string.IsNullOrEmpty(mm.EmailID))
                    {
                        adrTo.Add(mm.EmailID);
                        // adrTo.Add("<EMAIL>");
                    }
                }
                string strEmailFrom = SessionManager.LoginEmail;
                // string strEmailFrom = "<EMAIL>";


                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(HTMlString), adrTo, adrFrom, null, null, adrReplyTo, true);
                OutgoingMailManager.SendUserMailDocument(strSubject, adrTo, adrFrom, null, null, null, HTMlString, "", "");


            }
            catch (Exception)
            {
                bodyHTML = "";
            }



        }
        public void SendClosePoHUBMailGroupBOMManager(string strSubject, int? BomID, out string bodyHTML)
        {// send mail in group
            try
            {
                bodyHTML = "";
                StringBuilder strMail = new StringBuilder();
                string HTMlString = "";
                string StrmailData = "";
                string FilePath = Server.MapPath("~/Templates/CloseBOMManagerMail.htm");
                StreamReader sr = File.OpenText(FilePath);
                HTMlString = sr.ReadToEnd();
                sr.Close();
                sr.Dispose();
                //BOM bom = BOM.Get(BomID);
                BOMManagerContract BOMList = new BOMManagerContract();
                BOMList = BOMManagerContract.Get(BomID);

                HTMlString = HTMlString.Replace("#Code#", BOMList.BOMManagerCode + "( " + SessionManager.ClientName + " )");
                HTMlString = HTMlString.Replace("#Name#", BOMList.BOMManagerName);
                HTMlString = HTMlString.Replace("#Company#", BOMList.CompanyName);
                HTMlString = HTMlString.Replace("#Contact#", BOMList.ContactName);
                HTMlString = HTMlString.Replace("#Currency#", BOMList.CurrencyCode);
                HTMlString = HTMlString.Replace("#QuoteRequired#", Functions.FormatDate(BOMList.QuoteRequired));
                HTMlString = HTMlString.Replace("#NOTES#", BOMList.Notes);

                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> lst = BOMManagerContract.GetBOMListForCustomerRequirement(BomID, cid);
                //List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQForMail(BomID, SessionManager.ClientID);
                foreach (BOMManagerContract cReq in lst)
                {
                    string Line = "<tr>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.CustomerRequirementNumber + "<br />" + cReq.ReqTypeText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Quantity + "<br />" + cReq.ReqForTraceabilityText + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate) + "<br />" + cReq.CustomerPart + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ManufacturerCode + "<br />" + cReq.DateCode + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.ProductName + "<br />" + cReq.PackageName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (SessionManager.IsPOHub == true ? cReq.ClientName : cReq.CompanyName) + "<br />" + Functions.FormatDate(cReq.DatePromised) + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMManagerCurrencyCode) + "<br />" + cReq.SalesmanName + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.Instructions + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + cReq.MSL + "<br />" + (cReq.FactorySealed == false ? "NO" : "YES") + "</td>";
                    Line = Line + "<td class=\"Textclass\">" + (cReq.AlternativesAccepted == false ? "NO" : "YES") + "<br />" + (cReq.RepeatBusiness == false ? "NO" : "YES") + "</td>";
                    Line = Line + "</tr>";
                    strMail.Append(Line);


                }
                StrmailData = Convert.ToString(strMail);
                HTMlString = HTMlString.Replace("#trline#", StrmailData);
                bodyHTML = HTMlString;
                List<BLL.MailGroupMember> lstMail = null;
                lstMail = BLL.MailGroupMember.GetEmailListByGroup("HUBRFQ Group");
                MailAddressCollection adrTo = null;
                adrTo = new MailAddressCollection();
                foreach (BLL.MailGroupMember mm in lstMail)
                {
                    if (!string.IsNullOrEmpty(mm.EmailID))
                    {
                        adrTo.Add(mm.EmailID);
                        // adrTo.Add("<EMAIL>");
                    }
                }
                string strEmailFrom = SessionManager.LoginEmail;
                // string strEmailFrom = "<EMAIL>";


                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(HTMlString), adrTo, adrFrom, null, null, adrReplyTo, true);
                OutgoingMailManager.SendUserMailDocument(strSubject, adrTo, adrFrom, null, null, null, HTMlString, "", "");


            }
            catch (Exception)
            {
                bodyHTML = "";
            }



        }

        [WebMethod()]
        public void NotifyBomManagerCloseToHub(string strToLoginsArray, string strToGroupsArray, string strSubject, string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo, string aryRecipientLoginIDsCC)
        {
            try
            {
                // [002] code start Here
                string bodyHTML = "";
                // This is use to send mail in HUBRFQ Group member
                SendClosePoHUBMailGroupBOMManager(strSubject, bomId, out bodyHTML);
                // [002] code End Here




                string strMessage = MailTemplateManager.GetMessage_NotifyCloseBomManager(bomId, bomCode, bomName, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (mgm.LoginNo > 0)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                        }
                    }
                }
                // [006] code Start Here
                MailAddressCollection addrCC = null;
                GetMailIDForCC(aryRecipientLoginIDsCC, strSubject, strMessage, bomCompanyNo, out addrCC);
                // [006] code End Here
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    //If To have SendEmail false then send only cc 
                    if (!IsSendEmail && i == 0)
                    {
                        adrTo = new MailAddressCollection();
                        //adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        continue;
                    }

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        //  OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        if (i == 0)
                            OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        else
                            OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                        //addrCC = null;
                    }
                }

            }
            catch { }
        }

        [WebMethod()]
        public void NotifyPurchaseRequestBomManager(string strToLoginsArray, string strToGroupsArray, string strSubject, string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo, string aryRecipientLoginIDsCC, string AssignedUserType)
        {
            try
            {
                // [002] code start Here
                string bodyHTML = "";
                // This is use to send mail in HUBRFQ Group member
                SendPoHUBMailGroupBOMManager(strSubject, bomId, out bodyHTML);
                // [002] code End Here

                List<string> CCList = aryRecipientLoginIDsCC.Split('|').ToList();
                List<string> GroupIds = CCList.Where(x => x.Contains("_MailGroup")).ToList();
                CCList.RemoveAll(x => x.Contains("_MailGroup"));
                if (AssignedUserType == "Individual")
                {
                    CCList.Add(strToLoginsArray);
                }
                //else if (AssignedUserType == "Group")
                //{
                //    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(strToLoginsArray));
                //    CCList.AddRange(lstGroupMembers.Select(x => x.LoginNo.ToString()));
                //}
                //CCList.Add(strToLoginsArray);
                CCList.RemoveAll(x => x == "");
                string strMessage = MailTemplateManager.GetMessage_NotifyPurchaseRequestBomManager(bomCode, bomName, bomId, bomCompanyName, bomCompanyNo);
                Array aryToLogins = CCList.ToArray(); //Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = GroupIds.Select(x => x.Substring(0, x.IndexOf('_'))).ToArray(); ////Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (mgm.LoginNo > 0)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                        }
                    }
                }
                // [006] code Start Here
                MailAddressCollection addrCC = null;
                GetMailIDForCC(aryRecipientLoginIDsCC, strSubject, strMessage, bomCompanyNo, out addrCC);
                // [006] code End Here
                //do the sending
                lstToLoginID = lstToLoginID.Distinct().ToList(); ;
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    //If To have SendEmail false then send only cc 
                    if (!IsSendEmail && i == 0)
                    {
                        adrTo = new MailAddressCollection();
                        //adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        continue;
                    }

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        //  OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        if (i == 0)
                            OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        else
                            OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                        //addrCC = null;
                    }
                }
                if (AssignedUserType == "Group")
                {
                    NotifyGroupPurchaseRequestBomManager(Convert.ToInt32(strToLoginsArray), strSubject, bodyHTML);
                }

            }
            catch (Exception ex)
            {

            }
        }

        [WebMethod()]
        public void NotifyGroupPurchaseRequestBomManager(int GroupNo, string strSubject, string bodyHtml)
        {
            try
            {
                List<BLL.MailGroupMember> lstMail = null;
                lstMail = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(GroupNo));
                MailAddressCollection adrTo = null;
                adrTo = new MailAddressCollection();
                foreach (BLL.MailGroupMember mm in lstMail)
                {
                    if (!string.IsNullOrEmpty(mm.EmailID))
                    {
                        adrTo.Add(mm.EmailID);
                        // adrTo.Add("<EMAIL>");
                    }
                }
                string strEmailFrom = SessionManager.LoginEmail;
                // string strEmailFrom = "<EMAIL>";


                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                // OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(HTMlString), adrTo, adrFrom, null, null, adrReplyTo, true);
                OutgoingMailManager.SendUserMailDocument(strSubject, adrTo, adrFrom, null, null, null, bodyHtml, "", "");

            }
            catch (Exception ex)
            {

            }
        }

        // [006] code Start Here

        public void GetMailIDForCC(string aryRecipientLoginIDsCC, string strSubject, string strMessage, int? bomCompanyNo, out MailAddressCollection addrCC)
        {
            // send mail in group
            try
            {
                addrCC = null;
                MailAddressCollection adrCC = new MailAddressCollection();
                Array aryToLogins = Functions.JavascriptStringToArray(aryRecipientLoginIDsCC);
                List<int> lstToLoginID = new List<int>();
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );


                    bool IsSendEmail = false;
                    string strEmailTo = "";

                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }

                    if (IsSendEmail)
                    {
                        adrCC.Add(strEmailTo);

                    }
                }
                addrCC = adrCC;

            }
            catch (Exception)
            {
                addrCC = null;
            }



        }
        // [006] code End Here
        [WebMethod()]
        public void NotifyPurchaseRequestBom(string strToLoginsArray, string strToGroupsArray, string strSubject, string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo, string aryRecipientLoginIDsCC)
        {
            try
            {
                // [002] code start Here
                string bodyHTML = "";
                // This is use to send mail in HUBRFQ Group member
                SendPoHUBMailGroup(strSubject, bomId, out bodyHTML);
                // [002] code End Here

                string strMessage = MailTemplateManager.GetMessage_NotifyPurchaseRequestBom(bomCode, bomName, bomId, bomCompanyName, bomCompanyNo);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (mgm.LoginNo > 0)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                        }
                    }
                }
                // [006] code Start Here
                MailAddressCollection addrCC = null;
                GetMailIDForCC(aryRecipientLoginIDsCC, strSubject, strMessage, bomCompanyNo, out addrCC);
                // [006] code End Here
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    //If To have SendEmail false then send only cc 
                    if (!IsSendEmail && i == 0)
                    {
                        adrTo = new MailAddressCollection();
                        //adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        continue;
                    }

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        //  OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                        if (i == 0)
                            OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, addrCC, null, adrReplyTo, true);
                        else
                            OutgoingMailManager.SendUserMail(strSubject, bodyHTML, adrTo, adrFrom, null, null, adrReplyTo, true);
                        //addrCC = null;
                    }
                }

            }
            catch { }
        }

        //for RL RP-1710
        // [006] code End Here
        [WebMethod()]
        public void NotifyReverseLogisticsMatch(string strToLoginsArray, string strToGroupsArray, string strSubject, string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo, string aryRecipientLoginIDsCC, string PartNo, string PartLinesRL)
        {
            try
            {


                string strMessage = MailTemplateManager.GetMessage_ReverseLogistics(bomCode, bomName, bomId, bomCompanyName, bomCompanyNo, PartNo, PartLinesRL);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (mgm.LoginNo > 0)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                        }
                    }
                }
                // [006] code Start Here
                //MailAddressCollection addrCC = null;
                // GetMailIDForCC(aryRecipientLoginIDsCC, strSubject, strMessage, bomCompanyNo, out addrCC);
                // [006] code End Here
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , bomCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }


                }

            }
            catch { }
        }

        //end


        [WebMethod()]
        public void NotifyCustomerRMAToPoHub(string strToLoginsArray, string strToGroupsArray, string strSubject, int intCRMAID)
        {
            BLL.CustomerRma crma = BLL.CustomerRma.Get(intCRMAID);
            try
            {
                string strMessage = MailTemplateManager.GetMessage_NewCustomerRMA(crma);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , crma.CompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }


        [WebMethod()]
        public void NotifySupplierRMAToPoHub(string strToLoginsArray, string strToGroupsArray, string strSubject, int intSRMAID)
        {
            BLL.SupplierRma srma = BLL.SupplierRma.Get(intSRMAID);
            try
            {
                string strMessage = MailTemplateManager.GetMessage_NewSupplierRMA(srma);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , srma.CompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyCreditNotesToPoHub(string strToLoginsArray, string strToGroupsArray, string strSubject, int intCreditId)
        {
            BLL.Credit credit = BLL.Credit.Get(intCreditId);
            try
            {
                string strMessage = MailTemplateManager.GetMessage_NewCredit(credit);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , credit.CompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyDebitNotesToPoHub(string strToLoginsArray, string strToGroupsArray, string strSubject, int intDebitId)
        {
            BLL.Debit debit = BLL.Debit.Get(intDebitId);
            try
            {
                string strMessage = MailTemplateManager.GetMessage_NewDebit(debit);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , debit.CompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }


        //[WebMethod()]
        //public void NotifyMessageForAssignedPOHUBUser(string strToLoginsArray, string strSubject, string strMessage, string BOMIdList)
        //{

        //    try
        //    {                
        //        Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray); //Email ids                           
        //        MailAddressCollection adrTo = null;
        //        System.Net.Mail.MailAddress adrFrom = new MailAddress(SessionManager.LoginEmail);


        //        bool isSentSuccessfully = false;
        //        //string sectionName = reportNo == (int)Report.List.RequirementWithBOM ? "BOM" : "PurchaseHub";
        //        //string strTitle = reportNo == (int)Report.List.RequirementWithBOM ? "HUBRFQ" : "Price Request";
        //        //for (int i = 0; i < aryToLogins.Length; i++)
        //        //{
        //        //    string csvFile = string.Empty;
        //        //    adrTo = new MailAddressCollection();
        //        //    if (((string[])(aryToLogins))[i] != "")
        //        //    {
        //        //        //for (int iCount = 0; i < aryToCompanyIds.Length; iCount++)
        //        //        //{
        //        //        csvFile = FileUploadManager.ExportToCSVForClientList(reportNo, Id, CurrencyCode, Convert.ToInt32(aryToCompanyIds.GetValue(i)));
        //        //        BLL.CSVExportLog.Insert(
        //        //                     Convert.ToInt32(aryToCompanyIds.GetValue(i))
        //        //                     , csvFile
        //        //                     , SessionManager.LoginID
        //        //                     , sectionName
        //        //                 );
        //        //        adrTo.Add(aryToLogins.GetValue(i).ToString());
        //                strMessage = OutgoingMailManager.BodyMailMessage(strTitle, arySupplierNames.GetValue(i).ToString());
        //                isSentSuccessfully = OutgoingMailManager.SendAttachedDocumentToUsers(strSubject, adrTo, adrFrom, null, null, null, strMessage, csvFile, null);
        //                //}
        //            //}

        //       //}


        //    }
        //    //}

        //    catch { }
        //}
        [WebMethod()]
        public void NotifyAssignedToPOHUBUser(string strToLoginsArray, string strToGroupsArray, string strSubject, string BOMName, string selectedUserName)
        {
            try
            {
                string strMessage = OutgoingMailManager.BodyMailMessageForAssignToMe(BOMName, selectedUserName);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);

                //selectedUser
                //Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                if (strToLoginsArray.Contains(","))
                {
                    string[] myArray = strToLoginsArray.Split(',');
                    aryToLogins = myArray;
                }
                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyAssignedToPOHUBUserAS6081(string strToLoginsArray, string strToGroupsArray, string strSubject, string BOMName, string selectedUserName, string bomIds = "", string ByName = "")
        {
            try
            {
                string strMessage = OutgoingMailManager.BodyMailMessageForAssignToMeAS6081(BOMName, selectedUserName, bomIds, ByName);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);

                //selectedUser
                //Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                if (strToLoginsArray.Contains(","))
                {
                    string[] myArray = strToLoginsArray.Split(',');
                    aryToLogins = myArray;
                }
                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyAssignedToRequirementHubUserAS6081(string strToLoginsArray, string strToGroupsArray, string strSubject, string selectedUserName, string ByName, StringBuilder MessageBody)
        {
            try
            {
                string strMessage = "";//OutgoingMailManager.BodyMailMessageForAssignToMeAS6081(BOMName, selectedUserName, bomIds, ByName);
                StringBuilder strBuilder = new StringBuilder();
                strBuilder.Append("Dear " + selectedUserName + ",<br/><br/>Following HUBRFQ has been assigned to you by " + ByName + " on date: " + Functions.FormatDate(DateTime.Now) + ".<br/><br/>");
                string msgFooter = "<br/><br/>Please take action as soon as possible.<br />Kind Regards,<br />Global Trader";
                strMessage = strBuilder.ToString() + MessageBody.ToString() + msgFooter;
                //st
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);

                //selectedUser
                //Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                if (strToLoginsArray.Contains(","))
                {
                    string[] myArray = strToLoginsArray.Split(',');
                    aryToLogins = myArray;
                }
                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }
        [WebMethod()]
        public void NotifyRequesterToPOHUBUserAS6081(string strToLoginsArray, string strToGroupsArray, string strSubject, string BOMName, string selectedUserName, string bomIds = "", string RequesterName = "")
        {
            try
            {
                string strMessage = OutgoingMailManager.BodyMailMessageForRequesterToMeAS6081(BOMName, selectedUserName, bomIds, RequesterName);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);

                //selectedUser
                //Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                if (strToLoginsArray.Contains(","))
                {
                    string[] myArray = strToLoginsArray.Split(',');
                    aryToLogins = myArray;
                }
                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void NotifyRequesterToRequirementHUBUserAS6081(string strToLoginsArray, string strToGroupsArray, string strSubject, string selectedUserName, string RequesterName, StringBuilder MessageBody)
        {
            try
            {
                string strMessage = "";//OutgoingMailManager.BodyMailMessageForRequesterToMeAS6081(BOMName, selectedUserName, bomIds, RequesterName);

                StringBuilder strBuilder = new StringBuilder();
                strBuilder.Append("Dear " + RequesterName + ",<br/><br/>Following HUBRFQ has been assigned to " + selectedUserName + " on date: " + Functions.FormatDate(DateTime.Now) + ".<br/><br/>");
                string msgFooter = "<br/><br/>Please take action as soon as possible.<br />Kind Regards,<br />Global Trader";
                strMessage = strBuilder.ToString() + MessageBody.ToString() + msgFooter;
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);

                List<int> lstToLoginID = new List<int>();
                if (strToLoginsArray.Contains(","))
                {
                    string[] myArray = strToLoginsArray.Split(',');
                    aryToLogins = myArray;
                }
                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }
                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        //IHS Add Req Mail Code start
        [WebMethod()]
        public void SendMessageAddReq(int intCustomerRequirementID, string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, string strUsermessage)
        {
            try
            {
                string strMessageNew = "";
                string strMessagMerge = "";
                if (strUsermessage != null && strUsermessage != "")
                {
                    strMessagMerge = strMessage + " " + strUsermessage;
                }
                else
                {
                    strMessagMerge = strMessage;
                }
                BLL.CustomerRequirement cr = BLL.CustomerRequirement.Get(intCustomerRequirementID);
                if (cr != null)
                {
                    if (cr != null)
                        strMessageNew = MailTemplateManager.GetMessage_NewCustomerRequirement(cr, strMessagMerge);

                }

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                   // , strMessage
                   , strMessageNew
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
            }
            catch { }
        }

        #region ECCN Client Notification Sending Email 
        [WebMethod()]
        public void SendECCNMessageEditReq(int intCustomerRequirementID, string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, int? intloginid)
        {
            try
            {
                string strMessageNew = "";
                strSubject = "";
                strToLoginsArray = "";
                BLL.CustomerRequirement cr = BLL.CustomerRequirement.Get(intCustomerRequirementID);
                if (cr != null && cr.ECCNNotify == true)
                {
                    if (!string.IsNullOrEmpty(cr.ECCNCode))
                    {
                        #region ECCN Client Notification Condition check
                        strMessageNew = MailTemplateManager.GetMessage_NewCustomerRequirementECCN(cr);
                        strSubject = cr.EccnSubject;
                        if (cr.SupportTeamMemberNo > 0)
                        {
                            strToLoginsArray = Convert.ToString(intloginid) + "||" + Convert.ToString(cr.SupportTeamMemberNo);
                        }
                        else
                        {
                            strToLoginsArray = Convert.ToString(intloginid);
                        }
                        Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                        Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                        List<int> lstToLoginID = new List<int>();

                        //add all the individual logins to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToLogins.Length; i++)
                        {
                            int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                            if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                        }

                        //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToGroups.Length; i++)
                        {
                            List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                            foreach (MailGroupMember mgm in lstGroupMembers)
                            {
                                if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                            }
                        }

                        //do the sending
                        for (int i = 0; i < lstToLoginID.Count; i++)
                        {
                            BLL.MailMessage.Insert(
                                SessionManager.LoginID
                            , lstToLoginID[i]
                            , strSubject
                           // , strMessage
                           , strMessageNew
                            , intCompanyNo
                            , SessionManager.LoginID
                            );

                            // do email to their external email-id
                            bool IsSendEmail = false;
                            string strEmailTo = "";
                            //[001] code start
                            LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                            if (objLoginPref != null)
                            {
                                IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                                strEmailTo = objLoginPref.Email;
                            }
                            //[001] code end
                            string strEmailFrom = SessionManager.LoginEmail;
                            MailAddressCollection adrTo = null;

                            if (IsSendEmail)
                            {
                                adrTo = new MailAddressCollection();
                                adrTo.Add(strEmailTo);
                                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                                OutgoingMailManager.SendUserMail(strSubject, strMessageNew, adrTo, adrFrom, null, null, adrReplyTo, true);
                            }

                        }
                        #endregion ECCN Client Notification Condition check end
                    }
                }
            }
            catch { }
        }




        [WebMethod()]
        public void SendECCNMessageAddReq(int intCustomerRequirementID, string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, string strUsermessage)
        {
            try
            {
                string strMessageNew = "";
                strSubject = "";
                strToLoginsArray = "";
                BLL.CustomerRequirement cr = BLL.CustomerRequirement.Get(intCustomerRequirementID);
                if (cr != null && cr.ECCNNotify == true)
                {
                    if (!string.IsNullOrEmpty(cr.ECCNCode))
                    {
                        #region ECCN Client Notification Condition check
                        strMessageNew = MailTemplateManager.GetMessage_NewCustomerRequirementECCN(cr);
                        strSubject = cr.EccnSubject;
                        if (cr.SupportTeamMemberNo > 0)
                        {
                            strToLoginsArray = Convert.ToString(cr.UpdatedBy) + "||" + Convert.ToString(cr.SupportTeamMemberNo);
                        }
                        else
                        {
                            strToLoginsArray = Convert.ToString(cr.UpdatedBy);
                        }
                        Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                        Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                        List<int> lstToLoginID = new List<int>();

                        //add all the individual logins to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToLogins.Length; i++)
                        {
                            int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                            if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                        }

                        //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToGroups.Length; i++)
                        {
                            List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                            foreach (MailGroupMember mgm in lstGroupMembers)
                            {
                                if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                            }
                        }

                        //do the sending
                        for (int i = 0; i < lstToLoginID.Count; i++)
                        {
                            BLL.MailMessage.Insert(
                                SessionManager.LoginID
                            , lstToLoginID[i]
                            , strSubject
                           // , strMessage
                           , strMessageNew
                            , intCompanyNo
                            , SessionManager.LoginID
                            );

                            // do email to their external email-id
                            bool IsSendEmail = false;
                            string strEmailTo = "";
                            //[001] code start
                            LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                            if (objLoginPref != null)
                            {
                                IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                                strEmailTo = objLoginPref.Email;
                            }
                            //[001] code end
                            string strEmailFrom = SessionManager.LoginEmail;
                            MailAddressCollection adrTo = null;

                            if (IsSendEmail)
                            {
                                adrTo = new MailAddressCollection();
                                adrTo.Add(strEmailTo);
                                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                                OutgoingMailManager.SendUserMail(strSubject, strMessageNew, adrTo, adrFrom, null, null, adrReplyTo, true);
                            }

                        }
                        #endregion ECCN Client Notification Condition check end
                    }
                }
            }
            catch { }
        }
        //SO ECCN Notification send
        [WebMethod()]
        public void NotifyECCNMessageSO(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, System.Int32? LoginID, int? SalesOrderNumber, int? SalesOrderLineID, int? SupportTeamMemberNo, int? salesOrderNo)
        {
            try
            {
                string strMessageNew = "";
                //strSubject = "";
                //strToLoginsArray = "";
                BLL.SalesOrderLine cr = BLL.SalesOrderLine.Get(SalesOrderLineID);
                if (cr != null && cr.ECCNNotify == true)
                {
                    if (!string.IsNullOrEmpty(cr.ECCNCode))
                    {
                        #region ECCN Client Notification Condition check
                        strMessageNew = MailTemplateManager.GetMessage_NotifySalesOrderECCN(cr);
                        strSubject = cr.EccnSubject;
                        Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                        Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                        List<int> lstToLoginID = new List<int>();

                        //add all the individual logins to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToLogins.Length; i++)
                        {
                            int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                            if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                        }

                        //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToGroups.Length; i++)
                        {
                            List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                            foreach (MailGroupMember mgm in lstGroupMembers)
                            {
                                if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                            }
                        }

                        //do the sending
                        for (int i = 0; i < lstToLoginID.Count; i++)
                        {
                            BLL.MailMessage.Insert(
                                SessionManager.LoginID
                            , lstToLoginID[i]
                            , strSubject
                           // , strMessage
                           , strMessageNew
                            , intCompanyNo
                            , SessionManager.LoginID
                            );

                            // do email to their external email-id
                            bool IsSendEmail = false;
                            string strEmailTo = "";
                            //[001] code start
                            LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                            if (objLoginPref != null)
                            {
                                IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                                strEmailTo = objLoginPref.Email;
                            }
                            //[001] code end
                            string strEmailFrom = SessionManager.LoginEmail;
                            MailAddressCollection adrTo = null;

                            if (IsSendEmail)
                            {
                                adrTo = new MailAddressCollection();
                                adrTo.Add(strEmailTo);
                                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                                OutgoingMailManager.SendUserMail(strSubject, strMessageNew, adrTo, adrFrom, null, null, adrReplyTo, true);
                            }

                        }
                        #endregion ECCN Client Notification Condition check end
                    }
                }
            }
            catch { }
        }
        //end

        //PO ECCN Notification send
        [WebMethod()]
        public void NotifyECCNMessagePO(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, System.Int32? LoginID, int? PurchaseOrderNumber, int? PurchaseOrderLineID, int? SupportTeamMemberNo, int? PurchaseOrderID)
        {
            try
            {
                string strMessageNew = "";
                //strSubject = "";
                //strToLoginsArray = "";
                BLL.PurchaseOrderLine cr = BLL.PurchaseOrderLine.Get(PurchaseOrderLineID);
                if (cr != null && cr.ECCNNotify == true)
                {
                    if (!string.IsNullOrEmpty(cr.ECCNCode))
                    {
                        #region ECCN Client Notification Condition check
                        strMessageNew = MailTemplateManager.GetMessage_NotifyPOECCN(cr);
                        strSubject = cr.EccnSubject;
                        Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                        Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                        List<int> lstToLoginID = new List<int>();

                        //add all the individual logins to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToLogins.Length; i++)
                        {
                            int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                            if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                        }

                        //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                        for (int i = 0; i < aryToGroups.Length; i++)
                        {
                            List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                            foreach (MailGroupMember mgm in lstGroupMembers)
                            {
                                if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                            }
                        }

                        //do the sending
                        for (int i = 0; i < lstToLoginID.Count; i++)
                        {
                            BLL.MailMessage.Insert(
                                SessionManager.LoginID
                            , lstToLoginID[i]
                            , strSubject
                           // , strMessage
                           , strMessageNew
                            , intCompanyNo
                            , SessionManager.LoginID
                            );

                            // do email to their external email-id
                            bool IsSendEmail = false;
                            string strEmailTo = "";
                            //[001] code start
                            LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                            if (objLoginPref != null)
                            {
                                IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                                strEmailTo = objLoginPref.Email;
                            }
                            //[001] code end
                            string strEmailFrom = SessionManager.LoginEmail;
                            MailAddressCollection adrTo = null;

                            if (IsSendEmail)
                            {
                                adrTo = new MailAddressCollection();
                                adrTo.Add(strEmailTo);
                                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                                OutgoingMailManager.SendUserMail(strSubject, strMessageNew, adrTo, adrFrom, null, null, adrReplyTo, true);
                            }

                        }
                        #endregion ECCN Client Notification Condition check end
                    }
                }
            }
            catch { }
        }


        [WebMethod()]
        public void NotifyECCNMessageIPOPO(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, System.Int32? LoginID, int? PurchaseOrderNumber, int? PurchaseOrderLineID, int? SupportTeamMemberNo, int? PurchaseOrderID)
        {
            try
            {
                string strMessageNew = "";
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                //strSubject = "";
                //strToLoginsArray = "";
                lst = PurchaseOrderLine.GetListForPurchaseOrder(PurchaseOrderID);
                if (lst.Count > 0)
                {
                    foreach (PurchaseOrderLine ln in lst)
                    {
                        if (ln != null && ln.ECCNNotify == true)
                        {
                            if (!string.IsNullOrEmpty(ln.ECCNCode))
                            {
                                #region ECCN Client Notification Condition check
                                strMessageNew = MailTemplateManager.GetMessage_NotifyPOECCN(ln);
                                strSubject = ln.EccnSubject;
                                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                                List<int> lstToLoginID = new List<int>();

                                //add all the individual logins to the send list, taking care not to add duplicates
                                for (int i = 0; i < aryToLogins.Length; i++)
                                {
                                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                                }

                                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                                for (int i = 0; i < aryToGroups.Length; i++)
                                {
                                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                                    foreach (MailGroupMember mgm in lstGroupMembers)
                                    {
                                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                                    }
                                }

                                //do the sending
                                for (int i = 0; i < lstToLoginID.Count; i++)
                                {
                                    BLL.MailMessage.Insert(
                                        SessionManager.LoginID
                                    , lstToLoginID[i]
                                    , strSubject
                                   // , strMessage
                                   , strMessageNew
                                    , intCompanyNo
                                    , SessionManager.LoginID
                                    );

                                    // do email to their external email-id
                                    bool IsSendEmail = false;
                                    string strEmailTo = "";
                                    //[001] code start
                                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                                    if (objLoginPref != null)
                                    {
                                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                                        strEmailTo = objLoginPref.Email;
                                    }
                                    //[001] code end
                                    string strEmailFrom = SessionManager.LoginEmail;
                                    MailAddressCollection adrTo = null;

                                    if (IsSendEmail)
                                    {
                                        adrTo = new MailAddressCollection();
                                        adrTo.Add(strEmailTo);
                                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                                        OutgoingMailManager.SendUserMail(strSubject, strMessageNew, adrTo, adrFrom, null, null, adrReplyTo, true);
                                    }

                                }
                                #endregion ECCN Client Notification Condition check end
                            }
                        }
                    }
                }
                lst = null;
            }
            catch { }
        }
        //end
        #endregion end ECCN Client Notification Sending Email



        #region [007] Bhooma & Sunil Kumar 15/07/2021 Added method for send email and notification of short shipment
        [WebMethod()]
        public void NotifyShortShipmentMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, int ShortShipmentId, string strLoginName, string strGroupName, int ShortShipmentStage, string strMessageTemp, string strMessageEmail, System.Int32 NoReplyId, System.String NoReplyEmail, System.Int32? MailCCId)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strShortShipmentEmailLog = string.Empty;

                //string strMessageTemp = string.Format("Please review this <a onclick=\"{0}\" href=\"javascript:void(0);\">ShortShipment</a>:", string.Format("$RGT_openShortShipmentWindow({0} ,{1})", goodsInLineId, ShortShipmentId));
                string strEmailName = strLoginName + "/" + strGroupName;

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strShortShipmentEmailLog = "Emailedto" + "��" + strEmailName;
                string strMessageEmailTemp = strMessage + "<br/><br/>" + strMessageEmail + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;

                strMessage = strMessage + "<br/><br/>" + strMessageTemp + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;


                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        //SessionManager.LoginID
                        NoReplyId
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    //string strEmailFrom = SessionManager.LoginEmail;
                    string strEmailFrom = NoReplyEmail;
                    MailAddressCollection adrTo = null;
                    MailAddressCollection adrCC = null;
                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        objLoginPref = LoginPreference.GetByLoginForSendEmail(MailCCId);
                        string MailCC = "";
                        if (objLoginPref != null)
                        {
                            MailCC = objLoginPref.Email;
                        }
                        adrCC = new MailAddressCollection();
                        adrCC.Add(MailCC);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, this.ChangeInOutlookFormShortShipment(strMessageEmailTemp, ShortShipmentId), adrTo, adrFrom, adrCC, null, adrReplyTo, true);
                    }
                    //save ShortShipment log
                    strShortShipmentEmailLog = "Emailedto" + "��" + strEmailTo;
                    ShortShipment.InsertEmailShortShipmentLog(ShortShipmentId, strShortShipmentEmailLog, SessionManager.LoginID);
                }



            }
            catch { }
        }

        //[007]
        [WebMethod()]
        public void NotifyPartWatchMatchMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, int? intCompanyNo, string strLoginName, string RequirementId, string RequirementNo = "")
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;
                string strMessage = string.Empty;
                strMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + RequirementNo.ToString();
                string strMessageTemp = string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">PartWatch Match (" + RequirementNo.ToString() + ")</a>", "javascript:void(0);", "Ord_CusReqDetail.aspx?req=" + RequirementId);

                string strEmailName = strLoginName;
                string strEmailMessage = string.Empty, strMessageEmailTemp = string.Empty;
                strEmailMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + RequirementNo.ToString();
                strMessageEmailTemp = string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">PartWatch Match (" + RequirementNo.ToString() + ")</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/" + "Ord_CusReqDetail.aspx?req=" + RequirementId);

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                strEmailMessage = strMessageEmailTemp + "<br/><br/>" + strEmailMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;
                strMessage = strMessageTemp + "<br/><br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, strEmailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
            }
            catch { }
        }


        [WebMethod()]
        public void NotifyHUBIPIPartWatchMatchMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, int? intCompanyNo, string strLoginName, string RequirementId, string RequirementNo = "", System.Int32? BomId = 0)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;
                string strMessage = string.Empty;
                //strMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + RequirementNo.ToString();
                strMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + RequirementNo.ToString();
                // strMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">" + RequirementNo.ToString() + "</a>", "javascript:void(0);", "Ord_CusReqDetail.aspx?req=" + RequirementId); ;
                //string strMessageTemp = string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">PartWatch Match (" + RequirementNo.ToString() + ")</a>", "javascript:void(0);", "Ord_CusReqDetail.aspx?req=" + RequirementId);
                string strMessageTemp = string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">PartWatch Match (" + RequirementNo.ToString() + ")</a>", "javascript:void(0);", "Ord_BOMDetail.aspx?BOM=" + BomId);

                string strEmailName = strLoginName;
                string strEmailMessage = string.Empty, strMessageEmailTemp = string.Empty;
                strEmailMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + RequirementNo.ToString();
                //strEmailMessage = "Hi,<br/>You have one partwatch match found for the requirement:" + string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">"+ RequirementNo.ToString() + "</a>", "javascript:void(0);", "Ord_CusReqDetail.aspx?req=" + RequirementId); ;
                //strMessageEmailTemp = string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">PartWatch Match (" + RequirementNo.ToString() + ")</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/" + "Ord_CusReqDetail.aspx?req=" + RequirementId);
                strMessageEmailTemp = string.Format("PartWatch Match Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">PartWatch Match (" + RequirementNo.ToString() + ")</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/" + "Ord_BOMDetail.aspx?BOM=" + BomId);

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                strEmailMessage = strMessageEmailTemp + "<br/><br/>" + strEmailMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;
                strMessage = strMessageTemp + "<br/><br/>" + strMessage + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, strEmailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
            }
            catch { }
        }

        /// <summary>
        /// Supplier Approval Quality Approve send mail to buyer.
        /// </summary>

        [WebMethod()]
        public void NotifyBuyerForSupplierApproval(string strToLoginsArray, string strToGroupsArray, string strSubject, int? intCompanyNo, string strLoginName, string PurchaseOrderId, string PurchaseOrderNo, string strMessage)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;
                string strYellowPopupMessage = string.Empty;
                string strEmailMessage = string.Empty;
                strMessage += "<br/>Purchase Order:" + PurchaseOrderNo.ToString();
                string strMessageTemp = string.Format("Purchase Order Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">Purchase Order</a>", "javascript:void(0);", "Ord_PODetail.aspx?po=" + PurchaseOrderId);
                string strEmailName = strLoginName;

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                strYellowPopupMessage = strMessage + "<br/>" + strMessageTemp + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;

                //string strMessageEmail = string.Format("Purchase Order Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">Purchase Order</a>", "javascript:void(0);", HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority + "/" + "Ord_PODetail.aspx?po=" + PurchaseOrderId);
                string strMessageEmail = string.Format("Purchase Order Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">Purchase Order</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/" + "Ord_PODetail.aspx?po=" + PurchaseOrderId);

                strEmailMessage = strMessage + "<br/>" + strMessageEmail + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;


                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strYellowPopupMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, strEmailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
                //save npr log

                //ReportNPR.InsertEmailNPRLog(nprId, strNPREmailLog, SessionManager.LoginID);
            }
            catch { }
        }
        public void NotifySupplierTermAndCondetion(string strEmailId, string strToGroupsArray, string strSubject, int? intCompanyNo, string strMessage, int ClientNo)
        {
            try
            {
                string EmailID = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SendToSupplier"]);
                MailAddressCollection adrTo = null;
                MailAddressCollection adrCC = new MailAddressCollection();
                System.Net.Mail.MailAddress adrFrom = new MailAddress(SessionManager.LoginEmail);
                System.Net.Mail.MailAddress ReplyTo = new MailAddress(SessionManager.LoginEmail);
                System.Net.Mail.MailAddress ReplyCC = new MailAddress(SessionManager.LoginEmail);
                adrCC.Add(ReplyCC);
                bool isSentSuccessfully = false;
                string pdfFile = string.Empty;
                adrTo = new MailAddressCollection();

                #region Code to attcahed Term and condetion.
                string sPhysicalPath = FileUploadManager.GetTemporaryUploadFilePath();
                string strTerms = FileUploadManager.GetTermsDocName_Client(ClientNo, "purchase");
                string strNewFileName = sPhysicalPath + DateTime.Now.ToString("yyyyMMddHHmmss") + "_New" + ".pdf";
                string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
                string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strTerms, sasURL, "terms");
                //if (File.Exists(strTerms))
                Uri blobUri = new Uri(bothirl);
                CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                if (blob.Exists())
                {
                    List<string> lst = new List<string>();
                    lst.Add(bothirl);
                    Document document2 = new Document();
                    PdfCopy writer = new PdfCopy(document2, new FileStream(strNewFileName, FileMode.Create));
                    if (writer == null)
                    {
                        return;
                    }
                    document2.Open();
                    foreach (string fileName in lst)
                    {
                        PdfReader reader = new PdfReader(fileName);
                        reader.ConsolidateNamedDestinations();
                        for (int i = 1; i <= reader.NumberOfPages; i++)
                        {
                            PdfImportedPage page = writer.GetImportedPage(reader, i);
                            writer.AddPage(page);
                        }

                        PRAcroForm form = reader.AcroForm;
                        if (form != null)
                        {
                            writer.CopyAcroForm(reader);
                        }

                        reader.Close();
                    }
                    writer.Close();
                    document2.Close();
                }
                #endregion
                pdfFile = strNewFileName;
                adrTo.Add(strEmailId.ToString());
                string bodyMessage = OutgoingMailManager.BodyMailMessage(strMessage, strEmailId.ToString());
                isSentSuccessfully = OutgoingMailManager.SendAttachedDocumentToUsers(strSubject, adrTo, adrFrom, adrCC, null, ReplyTo, bodyMessage, pdfFile, null);
                adrTo = null;
                if (File.Exists(strNewFileName))
                    File.Delete(strNewFileName);

            }

            catch { }
        }
        private string ChangeInOutlookFormShortShipment(string strMessage, int shortShipmentId)
        {
            //string strUrl = PageManager.GotoURL_NPR(goodsInLineId, nprId);
            return Regex.Replace(strMessage, "<a [^>]+>(.*?)</a>", "ShortShipment").Replace("\n", "<br/>");
        }
        [WebMethod()]
        public void NotifyGIMessage(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, string mailMessage)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    try
                    {
                        BLL.MailMessage.Insert(
                            SessionManager.LoginID
                        , lstToLoginID[i]
                        , strSubject
                        , strMessage
                        , intCompanyNo
                        , SessionManager.LoginID
                        );

                        // do email to their external email-id
                        bool IsSendEmail = false;
                        string strEmailTo = "";
                        //[001] code start
                        LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                        if (objLoginPref != null)
                        {
                            IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                            strEmailTo = objLoginPref.Email;
                        }
                        //[001] code end
                        string strEmailFrom = SessionManager.LoginEmail;
                        MailAddressCollection adrTo = null;

                        if (IsSendEmail)
                        {
                            adrTo = new MailAddressCollection();
                            adrTo.Add(strEmailTo);

                            System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                            System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                            OutgoingMailManager.SendUserMail(strSubject, mailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                        }
                    }
                    catch (Exception ex)
                    {
                        BLL.MailMessage.InsertMailErrorLog(SessionManager.LoginID, lstToLoginID[i], strSubject, strMessage,
                            SessionManager.LoginID, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                BLL.MailMessage.InsertMailErrorLog(SessionManager.LoginID, 0, "GI Module Mail Error", "NotifyGIMessage();",
                    SessionManager.LoginID, ex.Message);
            }
        }


        [WebMethod()]
        public void NotifySalesPersonPOClose(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, string mailMessage)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , SessionManager.LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = SessionManager.LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, mailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }

        #endregion
        #endregion
        [WebMethod()]
        public void SendGIQuery(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, string mailMessage, System.Boolean? IsSendMailTo, System.Int32? FromLoginId, System.String FromEmailAddress, ref List<int?> lstSentMailUserId)
        {
            try
            {
                if (SessionManager.LoginID != null)
                    lstSentMailUserId.Add(SessionManager.LoginID);
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    bool alreadyExist = lstSentMailUserId.Contains(intLoginID);
                    if (alreadyExist == false)
                    {
                        if (!lstToLoginID.Contains(intLoginID))
                        {
                            lstToLoginID.Add(intLoginID);
                            lstSentMailUserId.Add(intLoginID);
                        }

                    }

                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        bool alreadyExist = lstSentMailUserId.Contains(mgm.LoginNo);
                        if (alreadyExist == false)
                        {
                            if (!lstToLoginID.Contains(mgm.LoginNo))
                            {
                                lstToLoginID.Add(mgm.LoginNo);
                                lstSentMailUserId.Add(mgm.LoginNo);
                            }
                        }


                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    try
                    {
                        BLL.MailMessage.Insert(
                            FromLoginId
                        , lstToLoginID[i]
                        , strSubject
                        , strMessage
                        , intCompanyNo
                        , SessionManager.LoginID
                        );

                        if (IsSendMailTo == true)
                        {
                            // do email to their external email-id
                            bool IsSendEmail = false;
                            string strEmailTo = "";
                            //[001] code start
                            LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                            if (objLoginPref != null)
                            {
                                IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                                strEmailTo = objLoginPref.Email;
                            }
                            //[001] code end
                            string strEmailFrom = FromEmailAddress;
                            MailAddressCollection adrTo = null;

                            if (IsSendEmail)
                            {
                                adrTo = new MailAddressCollection();
                                adrTo.Add(strEmailTo);

                                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                                mailMessage = Regex.Replace(mailMessage, @"\r\n?|\n", "<br/>");
                                OutgoingMailManager.SendUserMail(strSubject, mailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        BLL.MailMessage.InsertMailErrorLog(FromLoginId, lstToLoginID[i], strSubject, strMessage,
                            SessionManager.LoginID, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                BLL.MailMessage.InsertMailErrorLog(0, 0, "GI Module Mail Error", "SendGIQuery",
                    SessionManager.LoginID, ex.Message);
            }
        }


        [WebMethod()]
        public void SendEPORFQ(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, System.Int32? LoginID, System.String LoginEmailFrom)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                      LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = LoginEmailFrom;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);


                    }
                }
            }
            catch { }
        }

        [WebMethod()]
        public void PowerAppNotifyMessageSO(string strToLoginsArray, string strToGroupsArray, string strSubject, string strMessage, int? intCompanyNo, System.Int32? LoginID, System.String LoginEmailFrom)
        {
            try
            {

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                      LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = LoginEmailFrom;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);

                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);


                    }
                }
            }
            catch { }
        }
        [WebMethod()]
        public void PowerAppCreateIPONotify(string strToLoginsArray, string strToGroupsArray, string strSubject, int ipo, int po, int? intCompanyNo, BLL.PurchaseOrder _po, bool isPO, System.Int32? LoginID, String EmailFrom)
        {
            try
            {
                //BLL.PurchaseOrder _po = BLL.PurchaseOrder.Get(po);
                //_po.InternalPurchaseOrderNo = ipo;
                string strMessage = string.Empty;
                if (isPO)
                {
                    // _po.InternalPurchaseOrderNo = ipo;
                    strMessage = MailTemplateManager.GetMessage_NotifyPOTOCreateIPO(_po);
                }
                else
                {
                    strMessage = MailTemplateManager.GetMessage_NotifyCreateIPO(_po);
                }
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                      LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strMessage
                    , intCompanyNo
                    , LoginID
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = EmailFrom;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(strMessage), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                }
            }
            catch { }
        }


        #region OGEL
        [WebMethod()]
        public void NotifySalesPersonForExportApprovalData(string strToLoginsArray, string strToGroupsArray, string strSubject, int? intCompanyNo, string strLoginName, string SalesOrderId, string SalesOrderNo, string strMessage, int? FromLoginId, string LoginEmail, int? SalesOrderLineNo)
        {
            try
            {
                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                Array aryToGroups = Functions.JavascriptStringToArray(strToGroupsArray);
                List<int> lstToLoginID = new List<int>();
                string strNPREmailLog = string.Empty;
                string strYellowPopupMessage = string.Empty;
                string strEmailMessage = string.Empty;
                strMessage += "<br/>Sales Order:" + SalesOrderNo.ToString();
                strMessage += "<br/>Sales Order Line No:" + SalesOrderLineNo.ToString();
                string strMessageTemp = string.Format("Sales Order Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">Sales Order</a>", "javascript:void(0);", "Ord_SODetail.aspx?so=" + SalesOrderId);
                string strEmailName = strLoginName;

                strEmailName = strEmailName.TrimEnd('/').TrimStart('/');
                strNPREmailLog = "Emailedto" + "��" + strEmailName;
                strYellowPopupMessage = strMessage + "<br/>" + strMessageTemp + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;

                string strMessageEmail = string.Format("Sales Order Details: <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">Sales Order</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/" + "Ord_SODetail.aspx?so=" + SalesOrderId);

                strEmailMessage = strMessage + "<br/>" + strMessageEmail + "<br/>" + Functions.GetGlobalResource("Printing", "Emailedto") + " " + strEmailName;


                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                }

                //add all the logins from the mailgroups to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToGroups.Length; i++)
                {
                    List<BLL.MailGroupMember> lstGroupMembers = BLL.MailGroupMember.GetListByGroup(Convert.ToInt32(aryToGroups.GetValue(i)));
                    foreach (MailGroupMember mgm in lstGroupMembers)
                    {
                        if (!lstToLoginID.Contains(mgm.LoginNo)) lstToLoginID.Add(mgm.LoginNo);
                    }
                }


                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    BLL.MailMessage.Insert(
                      FromLoginId
                    , lstToLoginID[i]
                    , strSubject
                    , strYellowPopupMessage
                    , intCompanyNo
                    , FromLoginId
                    );

                    // do email to their external email-id
                    bool IsSendEmail = false;
                    string strEmailTo = "";
                    //[001] code start
                    LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    if (objLoginPref != null)
                    {
                        IsSendEmail = objLoginPref.SendEmail.HasValue ? Convert.ToBoolean(objLoginPref.SendEmail) : false;
                        strEmailTo = objLoginPref.Email;
                    }
                    //[001] code end
                    string strEmailFrom = LoginEmail;
                    MailAddressCollection adrTo = null;

                    if (IsSendEmail)
                    {
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        OutgoingMailManager.SendUserMail(strSubject, strEmailMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
                    }

                }
                //save npr log

                //ReportNPR.InsertEmailNPRLog(nprId, strNPREmailLog, SessionManager.LoginID);
            }
            catch { }
        }

        #endregion


        [WebMethod()]
        public void NotifyHUBRFQProspectiveOffer(List<int> customerRequirementIds, string strToLoginsArray, string strSubject, List<ProspectiveOfferLines> offerLineIds, int proId, List<int> lineIds, List<ProspectiveOfferForPowerApp> saleManNotifications)
        {
            try
            {
                string strMessage = "";
                List<string> messages = new List<string>();

                Array aryToLogins = Functions.JavascriptStringToArray(strToLoginsArray);
                List<int> lstToLoginID = new List<int>();

                List<CustomerRequirement> crReqs = BLL.CustomerRequirement.List(customerRequirementIds);
                if (crReqs != null && crReqs.Count > 0)
                {
                    strMessage = MailTemplateManager.GetMessage_NewProspectiveOfferCusReq(crReqs, proId, lineIds, offerLineIds);
                    SendOutlookEmail(strMessage, crReqs, saleManNotifications);
                }

                //add all the individual logins to the send list, taking care not to add duplicates
                for (int i = 0; i < aryToLogins.Length; i++)
                {
                    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
                    if (intLoginID > 0)
                    {
                        if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
                    }
                }

                //do the sending
                for (int i = 0; i < lstToLoginID.Count; i++)
                {
                    string salesManName = crReqs.Where(x => x.Salesman == saleManNotifications[i].SalemanId).Select(x => x.SalesmanName).Distinct().FirstOrDefault();
                    string startText = MailTemplateManager.GetHeaderMessage_NewProspectiveOfferCusReq(salesManName);
                    StringBuilder sb = new StringBuilder();
                    string strSending = "";
                    sb.Append(startText);
                    sb.Append("\r\nThe system has detected a prospective offer that could fulfil a potential requirement for a customer you manage. The details are below:");
                    sb.Append(strMessage);
                    sb.Append("Kind Regards,\r\nGlobal Trader Team");
                    strSending = Convert.ToString(sb);

                    BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , lstToLoginID[i]
                    , strSubject
                    , strSending
                    , null
                    , SessionManager.LoginID
                    );
                }
            }
            catch { }
        }

        private static void SendOutlookEmail(string strMessage, List<CustomerRequirement> cReqs, List<ProspectiveOfferForPowerApp> saleManNotifications)
        {
            bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
            bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
            var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);
            var mailToUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_To_UAT"]);
            string toAddress = "";

            foreach (ProspectiveOfferForPowerApp salesman in saleManNotifications)
            {
                if (allowOnProd)
                {
                    toAddress = salesman.Email;
                }
                else if (isAllowed)
                {
                    toAddress = mailToUAT;
                }
                string salesManName = cReqs.Where(x => x.Salesman == salesman.SalemanId).Select(x => x.SalesmanName).Distinct().FirstOrDefault();
                string startText = MailTemplateManager.GetHeaderMessage_NewProspectiveOfferCusReq(salesManName);
                string contentTeams = "There are new Prospective Offer(s) from Purchase HUB. Please check your email notification [Subject: Prospective Offer Detected] for further details";
                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(salesman.PowerAppUri);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var jsonObject = new
                {
                    salePerson = toAddress,
                    header = startText,
                    table = strMessage,
                    content = contentTeams
                };

                var body = JsonConvert.SerializeObject(jsonObject);
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, client.BaseAddress);
                request.Content = new StringContent(body, Encoding.UTF8, "application/json");
                client.SendAsync(request).ConfigureAwait(false);
            }
        }

        [WebMethod()]
        public void RLStockNotification(string strSubject, List<CustomerRequirement> customerRequirements, bool isFromBOM, int? ID, string BomCompanyName, string BOMCode)
        {
            try
            {
                string strMessage = "";
                List<string> messages = new List<string>();

                List<MailGroupMember> mails = BLL.MailGroupMember.GetEmailListByGroup("Reverse Logistics");
                int batchSize = 20;
                for (int i = 0; i < customerRequirements.Count; i += batchSize)
                {
                    List<CustomerRequirement> crReqs = customerRequirements.Skip(i).Take(batchSize).ToList();
                    strMessage = MailTemplateManager.GetMessage_RLStock(crReqs, isFromBOM);
                    SendPowerAppNotification(strSubject, strMessage, mails, ID, isFromBOM, BomCompanyName, BOMCode);
                    SendGTEmail(strSubject, strMessage, mails, ID, isFromBOM, BomCompanyName, BOMCode);
                }
            }
            catch { }
        }

        private void SendGTEmail(string strSubject, string strTableMessage, List<MailGroupMember> mails, int? ID, bool isFromBOM, string BomCompanyName, string BOMCode)
        {
            StringBuilder sb = new StringBuilder();
            string strMessage = "";
            string strMail = "";
            if (isFromBOM)
            {
                string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                var hubrfqLink = url + string.Format("Ord_BOMDetail.aspx?BOM={0}", ID);
                string strHubrfq = "<a href=\"" + hubrfqLink + "\">" + BOMCode + "</a>";
                strMail = string.Format(Functions.GetGlobalResource("Messages", "RLStockIntroduction1"), strHubrfq, BomCompanyName, strTableMessage);
            }
            else
            {
                strMail = string.Format(Functions.GetGlobalResource("Messages", "RLStockIntroduction2"), strTableMessage);
            }
            strMessage = Convert.ToString(sb.Append(strMail));

            foreach (MailGroupMember mail in mails)
            {
                BLL.MailMessage.Insert(
                        SessionManager.LoginID
                    , mail.LoginNo
                    , strSubject
                    , strMessage
                    , null
                    , SessionManager.LoginID
                    );
            }
        }

        private void SendPowerAppNotification(string strSubject, string strMessage, List<MailGroupMember> mails, int? ID, bool isFromBOM, string BomCompanyName, string BOMCode)
        {
            bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
            bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
            var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);
            var mailToUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_To_UAT"]);
            string toAddress = "";

            foreach (MailGroupMember mail in mails)
            {
                if (allowOnProd)
                {
                    toAddress = mail.EmailID;
                }
                else if (isAllowed)
                {
                    toAddress = mailToUAT;
                }

                string strMail = "";
                if (isFromBOM)
                {
                    string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                    var hubrfqLink = url + string.Format("Ord_BOMDetail.aspx?BOM={0}", ID);
                    string strHubrfq = "<a href=\"" + hubrfqLink + "\">" + BOMCode + "</a>";
                    strMail = string.Format(Functions.GetGlobalResource("Messages", "RLStockIntroductionOutlook1"), strHubrfq, BomCompanyName);
                }
                else
                {
                    strMail = string.Format(Functions.GetGlobalResource("Messages", "RLStockIntroductionOutlook2"));
                }

                string message = "";
                StringBuilder sb = new StringBuilder();
                message = Convert.ToString(sb.Append(strMail));

                string uri = BLL.PowerApp.GetFlowUrlByFlowName("Reverse_Logistic_Notification");

                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(uri);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var jsonObject = new
                {
                    rlPerson = toAddress,
                    subject = strSubject,
                    table = strMessage,
                    content = message
                };

                var body = JsonConvert.SerializeObject(jsonObject);
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, client.BaseAddress);
                request.Content = new StringContent(body, Encoding.UTF8, "application/json");
                client.SendAsync(request).ConfigureAwait(false);
            }
        }
    }
}

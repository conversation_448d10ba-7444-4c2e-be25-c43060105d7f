﻿using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate;
using GlobalTrader2.Aggregator.UseCases.Common.NotificationAndSendEmail.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CustomerRequirementNoBid.Commands;

namespace GlobalTrader2.Aggregator.UseCases.Orders.BOM.CustomerRequirementNoBidFlow.Commands
{
    public class UpdateCustomerRequirementNoBidFlowHandler(IMediator mediator) : IRequestHandler<UpdateCustomerRequirementNoBidFlowCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator = mediator;

        public async Task<BaseResponse<int>> Handle(UpdateCustomerRequirementNoBidFlowCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<int>();
            var updateCustomerRequirementNoBidReq = await _mediator.Send(new UpdateCustomerRequirementNoBidCommand()
            {
                BomId = request.BomId,
                CustomerRequirementId = request.CustomerRequirementId,
                NoBidNotes = request.Notes,
                UpdatedBy = request.UpdatedBy,
            }, cancellationToken);

            if (updateCustomerRequirementNoBidReq.Success)
            {
                //check again template 
                var notificationBody = await _mediator.Send(new NoBidHubrfqSendMailTemplateQuery
                {
                    HUBRFQStatus = request.StatusMessage,
                    Code = request.BomCode,
                    ClientCurrencyCode = request.ClientCurrencyCode,
                    BomId = request.BomId,
                    LoginId = request.LoginId,
                    ClientId = request.ClientId,
                    IsNoBidAll = false,
                    IsPoHub = request.IsPoHub,
                    RequirementId = request.CustomerRequirementId,
                });

                var notificationCommand = new CreateNotificationAndSendEmailCommand
                {
                    ToLogins = [request.SalesManNo],
                    Subject = request.Subject,
                    Body = notificationBody.Data ?? "",
                    CompanyNo = request.BomCompanyNo,
                    LoginId = request.UpdatedBy ?? 0,
                    Sender = string.Empty,
                    LoginEmail = request.LoginEmail,
                };
                await _mediator.Send(notificationCommand, cancellationToken);
                response.Success = true;
            }
            return response;
        }
    }
}
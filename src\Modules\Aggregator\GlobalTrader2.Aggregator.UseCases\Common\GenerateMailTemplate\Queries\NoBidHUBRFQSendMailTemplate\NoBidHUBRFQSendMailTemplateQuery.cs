﻿using System.Globalization;

namespace GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.NoBidHUBRFQSendMailTemplate
{
    public class NoBidHubrfqSendMailTemplateQuery : IRequest<BaseResponse<string>>
    {
        public required string HUBRFQStatus { get; set; }
        public required string Code { get; set; }
        public required string ClientCurrencyCode { get; set; }
        public required int BomId { get; set; }
        public required int LoginId { get; set; }
        public required int ClientId { get; set; }
        public required bool IsNoBidAll { get; set; }
        public required bool IsPoHub { get; set; }  
        public int? RequirementId { get; set; }
    }
}
